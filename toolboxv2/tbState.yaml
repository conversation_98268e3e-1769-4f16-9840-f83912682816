api: {}
app: {}
installable: {}
mods:
  AdminDashboard.py:
    provider: SimpleCore
    shasum: 76f2a9064a4d60006a58dea65d18f82d123a621d7e3dcfc1049feb7739349dea
    url: https://simplecore.app/mods/AdminDashboard.py
    version: legacy
  AgentUtils.py:
    provider: SimpleCore
    shasum: e455139271480e3309a4b2c309aa8d81b02f46c3713bed656df8ec4d7322e333
    url: https://simplecore.app/mods/AgentUtils.py
    version: legacy
  AuthManager.py:
    provider: SimpleCore
    shasum: 9e9bf19db7b2d87db4f3381bbf86cc8df095445acfb2af5217dcc3371e4dcd7d
    url: https://simplecore.app/mods/AuthManager.py
    version: legacy
  CodeVerification.py:
    provider: SimpleCore
    shasum: 1c8b852f228d5f09f271a143cf04311fb1fb855c2cec5ea4c3e0ba1aa4e9a7bc
    url: https://simplecore.app/mods/CodeVerification.py
    version: 0.0.1
  FaissVectorStore.py:
    provider: SimpleCore
    shasum: 20e5a9894e0562ad3d020f65385a6e5c02e603296b49680a609032e82c03572f
    url: https://simplecore.app/mods/FaissVectorStore.py
    version: legacy
  FileWidget.py:
    provider: SimpleCore
    shasum: 03d9f1a11516913c7d1887db81ac7fc63297fb613c0f91eafcaa244e0a3e8e5c
    url: https://simplecore.app/mods/FileWidget.py
    version: 0.2.0
  KnowledgeBase.py:
    provider: SimpleCore
    shasum: 02707da43a1191b7621aa29b2d88ed74e655e2c9cd538ee21aaa5163e8f474ff
    url: https://simplecore.app/mods/KnowledgeBase.py
    version: legacy
  MinimalHtml.py:
    provider: SimpleCore
    shasum: 119e2674a0685e9f69c06c77581c9e0859e49850eb3df2ed9a1827febf248af2
    url: https://simplecore.app/mods/MinimalHtml.py
    version: 0.0.2
  ModManager.py:
    provider: SimpleCore
    shasum: a617aeaa0e1331606313b7bfbe73e3ea8e1fa34b574b53c40ef544c90b8015bd
    url: https://simplecore.app/mods/ModManager.py
    version: legacy
  ProcessManager.py:
    provider: SimpleCore
    shasum: dd5a99b7dfb079ee220a43533ca27735e24f0a3f6b6352dbdc809dfe17486ac8
    url: https://simplecore.app/mods/ProcessManager.py
    version: 0.0.1
  RedisVectorStore.py:
    provider: SimpleCore
    shasum: 30e7075d2c17666f5ceb296535f7e37d8daf57a4a6387cf6e9dcb5a50399dbd2
    url: https://simplecore.app/mods/RedisVectorStore.py
    version: legacy
  SchedulerManager.py:
    provider: SimpleCore
    shasum: cfd9ea91dedbd679eaf7152691f4befb23eddde0f44701d1db7040b3367b5553
    url: https://simplecore.app/mods/SchedulerManager.py
    version: 0.0.2
  SocketManager.py:
    provider: SimpleCore
    shasum: 47b1857dead925d4c991bcbfa37739ff5c7dd5450318339ae4af72b3743b8609
    url: https://simplecore.app/mods/SocketManager.py
    version: 0.1.9
  StorageUtil.py:
    provider: SimpleCore
    shasum: f38f43e7ea64d691eeaaa3ccb31b88eb1ea8a6efedb1afe93f3ad16450cc26a0
    url: https://simplecore.app/mods/StorageUtil.py
    version: legacy
  UserAccountManager.py:
    provider: SimpleCore
    shasum: ab255bd57d7b0a4d1d4c80f24eef433a9d801df4d3218d02ba53fc13fdc8ea24
    url: https://simplecore.app/mods/UserAccountManager.py
    version: legacy
  UserDashboard.py:
    provider: SimpleCore
    shasum: 3974416752909db53a2c83b716f8a9fa11d9ab3391a3d100d1658fe6bb9981d4
    url: https://simplecore.app/mods/UserDashboard.py
    version: legacy
  UserInstances.py:
    provider: SimpleCore
    shasum: 67cfc7b79a76cb48b8ba6d3188ce2443447e8d620319f10e375c37a48e096598
    url: https://simplecore.app/mods/UserInstances.py
    version: legacy
  WebSocketManager.py:
    provider: SimpleCore
    shasum: 40a351471201e563adda4b90fdd3774389072f4d0376d8ebbc4b7c093fe98227
    url: https://simplecore.app/mods/WebSocketManager.py
    version: 0.0.3
  __init__.py:
    provider: SimpleCore
    shasum: ddf7deb30f4076128913c1d2ecd79ddc7d0bd4de3065271c278a25c2e07f5504
    url: https://simplecore.app/mods/__init__.py
    version: legacy
  adapter.py:
    provider: SimpleCore
    shasum: 3794d27cd3f04f43fef403031166b54683b0e5b04540fbdf364274aaa97cae7c
    url: https://simplecore.app/mods/adapter.py
    version: legacy
  agent.py:
    provider: SimpleCore
    shasum: 2cba3eeca2fa3181110882779f84e07cc7c86aae438b9c8e2cbad2ae5591c554
    url: https://simplecore.app/mods/agent.py
    version: legacy
  arXivCrawler.py:
    provider: SimpleCore
    shasum: 5851f39ce7689360efed8d1b486bb11958801bd2801181f81218e968370e99dc
    url: https://simplecore.app/mods/arXivCrawler.py
    version: legacy
  board_widget.py:
    provider: SimpleCore
    shasum: 4659d3bb73d1dd5759a43052220a7c26c2498100a1554a482cdaa95f37a7d21c
    url: https://simplecore.app/mods/board_widget.py
    version: legacy
  builder.py:
    provider: SimpleCore
    shasum: 8309426b066c3e27c827e39115e1ccd78e090f8fb9a2350a8e68b982697384d9
    url: https://simplecore.app/mods/builder.py
    version: legacy
  chainUi.py:
    provider: SimpleCore
    shasum: b5b3efaaa969e3a54099f639df6b6f07944e85ba5346ee672f2d5795a8dfc43b
    url: https://simplecore.app/mods/chainUi.py
    version: legacy
  cli_functions.py:
    provider: SimpleCore
    shasum: f3322e6e73569abfe919dcdcf511fdaf95bb7ef94dff04244b88832099ac8d1d
    url: https://simplecore.app/mods/cli_functions.py
    version: 0.0.1
  client.py:
    provider: SimpleCore
    shasum: 80e7fd68a844283624dfd6cf579fc7fa657bc3b5107ba779111bef6974392ac3
    url: https://simplecore.app/mods/client.py
    version: legacy
  config.py:
    provider: SimpleCore
    shasum: 8886aecc9e0c3b48348f9f9ef8d1a88a1677fd5ab93c04e19f0286181447348d
    url: https://simplecore.app/mods/config.py
    version: legacy
  email_services.py:
    provider: SimpleCore
    shasum: 5f9e93ceb8031ffef47892dfb97eb82c396d56204c5f17151e0938bb32253aa7
    url: https://simplecore.app/mods/email_services.py
    version: legacy
  executors.py:
    provider: SimpleCore
    shasum: 4f2a8dab637e45c1583327b4f0f947328c32a30f65746521a1cb13c7a0372c44
    url: https://simplecore.app/mods/executors.py
    version: legacy
  extras.py:
    provider: SimpleCore
    shasum: b2371daf37c4aca592aa8665e14834bd63521bf9b5e3bafed7f0d5ed9553312b
    url: https://simplecore.app/mods/extras.py
    version: legacy
  fast_api.py:
    provider: SimpleCore
    shasum: e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855
    url: https://simplecore.app/mods/fast_api.py
    version: legacy
  fast_api_install.py:
    provider: SimpleCore
    shasum: c1368487ccc20616d06ba3d3c27ca1a18f56821b7c7cbdba8074b6ef22be0b20
    url: https://simplecore.app/mods/fast_api_install.py
    version: legacy
  fast_api_main.py:
    provider: SimpleCore
    shasum: 202c91d9edcf5bc934404578361d17589470c8b21f4c62e0d43ce4d03aabf8df
    url: https://simplecore.app/mods/fast_api_main.py
    version: legacy
  fast_lit.py:
    provider: SimpleCore
    shasum: eda2a774cd071534a61d2c7a98a441d151cf821446d23599040431ef78550c3b
    url: https://simplecore.app/mods/fast_lit.py
    version: legacy
  fast_nice.py:
    provider: SimpleCore
    shasum: f067f4176f8eb870543d66ccb712e8ecbb69c9bd8d36beb4b341cd3a1a78dff4
    url: https://simplecore.app/mods/fast_nice.py
    version: legacy
  filter.py:
    provider: SimpleCore
    shasum: 8d909c583958067ba1352549f48f29fcf979afc16cb6cbfcea9d70b2450c450b
    url: https://simplecore.app/mods/filter.py
    version: legacy
  isaa_modi.py:
    provider: SimpleCore
    shasum: a5a9659c68d9cd6a235bd57654332727aa9056c552633cc8e4e24bb47ceeca66
    url: https://simplecore.app/mods/isaa_modi.py
    version: legacy
  live.py:
    provider: SimpleCore
    shasum: 02426aaa34df513e9172aa8a2dfe6c94acf23ca0b082c5bdb14ff34579670eab
    url: https://simplecore.app/mods/live.py
    version: legacy
  local_instance.py:
    provider: SimpleCore
    shasum: cbd59d79f0eae2dd6c568e4504159ea13bfad53aaf39c912a626d47350c1d8d6
    url: https://simplecore.app/mods/local_instance.py
    version: legacy
  manager.py:
    provider: SimpleCore
    shasum: c0aa65c3c239ca30f9211218d7c0ae00b1f6989b994e7a694d4cd7f1d35ee3c2
    url: https://simplecore.app/mods/manager.py
    version: legacy
  mini.py:
    provider: SimpleCore
    shasum: 8545058ad881db86d1bc79615840a34a758ee4f48c53166b345d3f10e25e547b
    url: https://simplecore.app/mods/mini.py
    version: legacy
  modes.py:
    provider: SimpleCore
    shasum: 1e69373170945d5ab9bd3da2e6c8521a6822179d70a10dc9877b7656cc9873a5
    url: https://simplecore.app/mods/modes.py
    version: legacy
  module.py:
    provider: SimpleCore
    shasum: ac7d0fe2381e5b828f4b80311ca62b8f01ebafc95a2fc22559d24bbbdd5c66ba
    url: https://simplecore.app/mods/module.py
    version: legacy
  nGui.py:
    provider: SimpleCore
    shasum: a277b7db2967bfb4b71a78de23faa0ee205d0a392c4a8d5b175802e64458069f
    url: https://simplecore.app/mods/nGui.py
    version: legacy
  newui.py:
    provider: SimpleCore
    shasum: f0aa21049c187598c3c9ad3bceda776d757eb4bc2ab59af0b9bd1817a62f3824
    url: https://simplecore.app/mods/newui.py
    version: legacy
  one.py:
    provider: SimpleCore
    shasum: 132bbd80f78c5f979bbe10d655febbbbef9895a3b48011e4a9d3d9ef5c57381b
    url: https://simplecore.app/mods/one.py
    version: legacy
  parser.py:
    provider: SimpleCore
    shasum: 29a74e2fe24dd6d0af22ea263c7abeb2fb2c0b664e4bc61f665b903038d9c0e1
    url: https://simplecore.app/mods/parser.py
    version: legacy
  reddis_instance.py:
    provider: SimpleCore
    shasum: 078fe4d3fb64984d0e1117e8b3f7fc2be13dfee87eb0b32ec9fb4dc9deb71bd8
    url: https://simplecore.app/mods/reddis_instance.py
    version: legacy
  search_tool.py:
    provider: SimpleCore
    shasum: 68fd02690f19799924586fbd5f18371e83510231d5f4dcf50e880524465082b3
    url: https://simplecore.app/mods/search_tool.py
    version: legacy
  server.py:
    provider: SimpleCore
    shasum: 4e2f549b2afe88944b270d33368c668a73ecc1694d58475c319e2c334c6469e3
    url: https://simplecore.app/mods/server.py
    version: legacy
  session.py:
    provider: SimpleCore
    shasum: 674a97cdd6c2951e36417d71077d9a8c22be29d0239410350aa0b36eccf86a82
    url: https://simplecore.app/mods/session.py
    version: legacy
  taichiNumpyNumbaVectorStores.py:
    provider: SimpleCore
    shasum: 55300a51473dea98b56322fde82149640865048e3f99bc64bb92bd4133af5292
    url: https://simplecore.app/mods/taichiNumpyNumbaVectorStores.py
    version: legacy
  talk.py:
    provider: SimpleCore
    shasum: 6f0598476168e3ced6a6df69ac3a137e8ce1759b8adb43334b1b75c30ecb4053
    url: https://simplecore.app/mods/talk.py
    version: 0.0.1
  tb_adapter.py:
    provider: SimpleCore
    shasum: c080b12f587abc0f280c44e784d7aa5ea9ef9415d558363fd18825ca38742c34
    url: https://simplecore.app/mods/tb_adapter.py
    version: legacy
  tests.py:
    provider: SimpleCore
    shasum: 073e6d20114795ceecd415e944ad4b6c030a3cbe32218556797ce68ce570b3a1
    url: https://simplecore.app/mods/tests.py
    version: legacy
  types.py:
    provider: SimpleCore
    shasum: 79421790acf9a4d0a664b2a47c1455cfa4945191d006d5abc355c0786edc96ff
    url: https://simplecore.app/mods/types.py
    version: legacy
  ui.py:
    provider: SimpleCore
    shasum: 7156d8115072f81fb4cc66cf04b66bdf41211c4d507c7dac9c71e01a46e38a3a
    url: https://simplecore.app/mods/ui.py
    version: legacy
  util.py:
    provider: SimpleCore
    shasum: a9bbc6ca16964a33e724404e5bf3564f7cdb6a906e41e3ef64c07dcc0c0c54e0
    url: https://simplecore.app/mods/util.py
    version: legacy
  utils.py:
    provider: SimpleCore
    shasum: 51ac5215c2fae96e914bd17b588b3a9aa9de2a57c81570b9a3c3070e44a8004b
    url: https://simplecore.app/mods/utils.py
    version: legacy
  welcome.py:
    provider: SimpleCore
    shasum: 76513fb1b0447fa1f6bd4520ad96d9908d5a2d8a1c625beeace2b9f5c57b4fd8
    url: https://simplecore.app/mods/welcome.py
    version: 0.0.1
  widget.py:
    provider: SimpleCore
    shasum: 9da54a244e57c9a30b1ba23ae634c7a3ce82381f72c9387c41f4fb24e1705188
    url: https://simplecore.app/mods/widget.py
    version: legacy
  wiget_test.py:
    provider: SimpleCore
    shasum: 4870dfa760c7762b3e90d87425a64a5ec200694121adadfe89417bcb0f97a0af
    url: https://simplecore.app/mods/wiget_test.py
    version: legacy
runnable: {}
utils:
  Style.py:
    provider: git
    shasum: f0dda20d5d41c47dd0aab7a8ab6f853c1b4e2a61256b318939fb5f47f1afbb0a
    url: https://github.com/MarkinHaus/ToolBoxV2/tree/master/toolboxv2/utils/Style.py
    version: 0.1.21
  __init__.py:
    provider: git
    shasum: c5c452be011321686b787e85d3f6bbd63136198e71b7474a430965d5d1c8f79a
    url: https://github.com/MarkinHaus/ToolBoxV2/tree/master/toolboxv2/utils/__init__.py
    version: 0.1.21
  all_functions_enums.py:
    provider: git
    shasum: 11a4cc5be3e431185b32163621df149b56b01f0bc23adecc4220ab4c331703b5
    url: https://github.com/MarkinHaus/ToolBoxV2/tree/master/toolboxv2/utils/all_functions_enums.py
    version: 0.1.21
  api.py:
    provider: git
    shasum: d38abe9fb1ee6c380a814a40db535c2be862d318931ef507a8a50f718f684ae9
    url: https://github.com/MarkinHaus/ToolBoxV2/tree/master/toolboxv2/utils/api.py
    version: 0.1.21
  base_widget.py:
    provider: git
    shasum: 265f529763073e0cd0145879ae8ad131ef66f9d8a3028f5b5fdcbafa08e3f406
    url: https://github.com/MarkinHaus/ToolBoxV2/tree/master/toolboxv2/utils/base_widget.py
    version: 0.1.21
  blobs.py:
    provider: git
    shasum: 0dfc785c13472c95b6d7dfb5749a0e015fc5261e63dbbc0ea66f81e038b06b77
    url: https://github.com/MarkinHaus/ToolBoxV2/tree/master/toolboxv2/utils/blobs.py
    version: 0.1.21
  bottleup.py:
    provider: git
    shasum: da6df6b5def0f08b77d1e457b7edd2b1095f765438f0a39f5fad91573981ae35
    url: https://github.com/MarkinHaus/ToolBoxV2/tree/master/toolboxv2/utils/bottleup.py
    version: 0.1.21
  cache.py:
    provider: git
    shasum: c602e9be9cd10a5cc5ec849654ad54f98f56df9845f7bffc286516a3435ea06f
    url: https://github.com/MarkinHaus/ToolBoxV2/tree/master/toolboxv2/utils/cache.py
    version: 0.1.21
  client.py:
    provider: git
    shasum: 77f072971d42656c4ed893bc73fccc087a48985d469cc89b686643f09558d90d
    url: https://github.com/MarkinHaus/ToolBoxV2/tree/master/toolboxv2/utils/client.py
    version: 0.1.21
  conda_runner.py:
    provider: git
    shasum: 4028066da4390fc2dc5f2577834ec20de2632d7335dec8fc2f28e2710df820ae
    url: https://github.com/MarkinHaus/ToolBoxV2/tree/master/toolboxv2/utils/conda_runner.py
    version: 0.1.21
  cryp.py:
    provider: git
    shasum: c1b89a80db3a136309397d5b69e3b2a9ddec108a2eea24e628b45a681627c449
    url: https://github.com/MarkinHaus/ToolBoxV2/tree/master/toolboxv2/utils/cryp.py
    version: 0.1.21
  daemon_app.py:
    provider: git
    shasum: 4203c6bff6423e7dbb672aa019ab075521ddcb85516d38dc2e9d9539b85144eb
    url: https://github.com/MarkinHaus/ToolBoxV2/tree/master/toolboxv2/utils/daemon_app.py
    version: 0.1.21
  daemon_util.py:
    provider: git
    shasum: a476ee3a54a1366c36b3bc47397559fe067d708c1cf28f4287f3990e5a1175a7
    url: https://github.com/MarkinHaus/ToolBoxV2/tree/master/toolboxv2/utils/daemon_util.py
    version: 0.1.21
  file_handler.py:
    provider: git
    shasum: c0214a00860dae1b630f0ad0a8d12ff2b211e89ab9af13f6855ffd5e0d5b7a2c
    url: https://github.com/MarkinHaus/ToolBoxV2/tree/master/toolboxv2/utils/file_handler.py
    version: 0.1.21
  getting_and_closing_app.py:
    provider: git
    shasum: a20687119b5611228803ba9ea1e0d87b8bb44dde653ea341f6b6d03a8cfaab8a
    url: https://github.com/MarkinHaus/ToolBoxV2/tree/master/toolboxv2/utils/getting_and_closing_app.py
    version: 0.1.21
  gist_control.py:
    provider: git
    shasum: 00ab48854e103fb5e8403a7d29dc7cb97080ec2f619b8cb18b4e8fef25fcfe51
    url: https://github.com/MarkinHaus/ToolBoxV2/tree/master/toolboxv2/utils/gist_control.py
    version: 0.1.21
  helper_test_functions.py:
    provider: git
    shasum: 8e2f62fb46c1f407c44df954e63554834e0d6089790e058c456e8f5fd7a6b3d4
    url: https://github.com/MarkinHaus/ToolBoxV2/tree/master/toolboxv2/utils/helper_test_functions.py
    version: 0.1.21
  ipy_completer.py:
    provider: git
    shasum: c0d53880afef0d9032aec1bdb83473b50941c56401637bb9ca5fdcbd90b274d4
    url: https://github.com/MarkinHaus/ToolBoxV2/tree/master/toolboxv2/utils/ipy_completer.py
    version: 0.1.21
  keword_matcher.py:
    provider: git
    shasum: 0ab16ce2b640cededa5a35be17de08031a3fcfa9ba24d147ca9d684a72b6812a
    url: https://github.com/MarkinHaus/ToolBoxV2/tree/master/toolboxv2/utils/keword_matcher.py
    version: 0.1.21
  main_tool.py:
    provider: git
    shasum: e4700996678524569a002f25acc092a3743de33aa428abfad88265079f273222
    url: https://github.com/MarkinHaus/ToolBoxV2/tree/master/toolboxv2/utils/main_tool.py
    version: 0.1.21
  prox_util.py:
    provider: git
    shasum: 15531b5e59f540a0594c8e213519d6eb41102eef442aef9cb227080a74e2e16f
    url: https://github.com/MarkinHaus/ToolBoxV2/tree/master/toolboxv2/utils/prox_util.py
    version: 0.1.21
  proxy_app.py:
    provider: git
    shasum: 24c77cfa34ec415727e32e414816220eaebae6d6db865c375578e639bef57444
    url: https://github.com/MarkinHaus/ToolBoxV2/tree/master/toolboxv2/utils/proxy_app.py
    version: 0.1.21
  qr.py:
    provider: git
    shasum: 7c54800282919fb6c0c810f732680912ecf4566840cbb8b712364df6b8851aec
    url: https://github.com/MarkinHaus/ToolBoxV2/tree/master/toolboxv2/utils/qr.py
    version: 0.1.21
  reqbuilder.py:
    provider: git
    shasum: bc303c1b460bfbc5d428f0946a10c9b5606cac3f1b15ec8810f815b0593f5cd0
    url: https://github.com/MarkinHaus/ToolBoxV2/tree/master/toolboxv2/utils/reqbuilder.py
    version: 0.1.21
  server.py:
    provider: git
    shasum: 50db7a4b16298c4c4ca5eff62a2074eb4257c4baa319f6c589bb767122abb6f7
    url: https://github.com/MarkinHaus/ToolBoxV2/tree/master/toolboxv2/utils/server.py
    version: 0.1.21
  session.py:
    provider: git
    shasum: 37bae9630399550a1dbb2d61789a1c387ab798887108cf22b2bc9fab043906bb
    url: https://github.com/MarkinHaus/ToolBoxV2/tree/master/toolboxv2/utils/session.py
    version: 0.1.21
  show_and_hide_console.py:
    provider: git
    shasum: d5d5cad1ef8f9cfcc2adcc12c936103d580971d6504ee19509a2f1f4ec7afdbe
    url: https://github.com/MarkinHaus/ToolBoxV2/tree/master/toolboxv2/utils/show_and_hide_console.py
    version: 0.1.21
  singelton_class.py:
    provider: git
    shasum: 0410f2ce1a9d8a860cfc7d67407b2c77eb44f31fc2dbdb6a302c8623866da4b8
    url: https://github.com/MarkinHaus/ToolBoxV2/tree/master/toolboxv2/utils/singelton_class.py
    version: 0.1.21
  state_system.py:
    provider: git
    shasum: 512385c7f4eb0b753bfb5174ca4bfa07ba654faaf98291fb71989ad2dc54c004
    url: https://github.com/MarkinHaus/ToolBoxV2/tree/master/toolboxv2/utils/state_system.py
    version: 0.1.21
  tb_logger.py:
    provider: git
    shasum: 4609d05d21be81b065a1c8b5a65043dcc18698c6bd0051d68077a5d4cf8bde59
    url: https://github.com/MarkinHaus/ToolBoxV2/tree/master/toolboxv2/utils/tb_logger.py
    version: 0.1.21
  toolbox.py:
    provider: git
    shasum: 5ed727787212d9b88dd45b0621deb3ed80a6e12534a65f97bbb5c9d1bfcde3dd
    url: https://github.com/MarkinHaus/ToolBoxV2/tree/master/toolboxv2/utils/toolbox.py
    version: 0.1.21
  types.py:
    provider: git
    shasum: b56988e4a5f7e412c52962a0439ad1058345b09d70825b2c6efe5ca7d5ae38eb
    url: https://github.com/MarkinHaus/ToolBoxV2/tree/master/toolboxv2/utils/types.py
    version: 0.1.21
