/* tbjs/src/styles/tbjs-main.css */
@import "tailwindcss/utilities";

/* Optionale tbjs-spezifische Basis-Styles oder Komponenten-Layer hier */
/* Definiere hier die CSS-Variablen für deine Farbpalette,
   damit sie von der <PERSON> überschrieben werden können, falls gewünscht. */


/* ==========================================================================
   1. Core Variables & Themes
   ========================================================================== */
:root {
  --tb-color-primary-50: #eff6ff;
  --tb-color-primary-100: #dbeafe;
  /* ... alle primären Farben ... */
  --tb-color-primary-500: #3b82f6;
  --tb-color-primary-600: #2563eb;
  --tb-color-primary-700: #1d4ed8;

  --tb-color-background: #ffffff;
  --tb-color-text: #1f2937; /* gray-800 */
  --tb-color-border: #e5e7eb; /* gray-200 */
  --tb-color-accent: var(--tb-color-primary-600);
    /* Base Font & Sizing */
    --font-family-sans: 'Roboto', system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Oxygen, Ubuntu, Cantarell, 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif;
    --font-family-serif: Georgia, Cambria, 'Times New Roman', Times, serif;
    --font-family-mono: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, 'Liberation Mono', 'Courier New', monospace;
    --font-family-base: var(--font-family-sans); /* Hauptschriftart */

    /* Fluid Font Sizes (adjust base clamp value if needed) */
    --font-size-xs: clamp(0.75rem, 0.7rem + 0.25vw, 0.875rem);
    --font-size-sm: clamp(0.875rem, 0.8rem + 0.375vw, 1rem);
    --font-size-base: clamp(1rem, 0.9rem + 0.5vw, 1.125rem); /* Base */
    --font-size-lg: clamp(1.125rem, 1rem + 0.625vw, 1.3rem); /* Slightly larger L */
    --font-size-xl: clamp(1.3rem, 1.125rem + 0.75vw, 1.6rem); /* Slightly larger XL */
    --font-size-2xl: clamp(1.6rem, 1.375rem + 1vw, 2.1rem); /* etc. */
    --font-size-3xl: clamp(2rem, 1.75rem + 1.25vw, 2.7rem);
    --font-size-4xl: clamp(2.5rem, 2rem + 1.5vw, 3.3rem);

    /* Font Weights */
    --font-weight-light: 300;
    --font-weight-regular: 400;
    --font-weight-medium: 500;
    --font-weight-semibold: 600;
    --font-weight-bold: 700;

    /* Line Heights */
    --line-height-tight: 1.2;
    --line-height-snug: 1.375;
    --line-height-normal: 1.6; /* Slightly increased for readability */
    --line-height-relaxed: 1.75;
    --line-height-loose: 2;

    /* Letter Spacing */
    --letter-spacing-tight: -0.02em;
    --letter-spacing-normal: 0em;
    --letter-spacing-wide: 0.02em;

    /* Spacing Unit */
    --spacing: 1rem; /* Base spacing unit */
    --radius-sm: 4px;
    --radius-md: 8px;
    --radius-lg: 16px;

    /* Transitions */
    --transition-fast: 0.15s ease-in-out;
    --transition-medium: 0.3s ease-in-out;

    /* Scrollbar Base (can be overridden by theme) */
    --scrollbar-width: 8px;
    --scrollbar-height: 8px;

    /* Z-Index */
    --z-background: -1;
    --z-content: 1;
    --z-navigation: 100;
    --z-overlay: 500;
    --z-modal: 10000;
    --z-cookie-banner: 10010;
    --z-nav-controls: 10001;
    --z-nav-dropdown: 10005;

    --button-hover-bg: var(--link-hover-color); --text-shadow-current: var(--text-shadow-light); /* Shadow for text on glass */
    /* Font Smoothing */
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    text-rendering: optimizeLegibility;


    --primary-color: rgba(0,0,0,0);
    --FILL: 0;
    --wght: 200;
    --GRAD: 50;
    --opsz: 24;
    --letter-spacing-wider: 0.05em;

    /* Text Shadow for Readability over Glass */
    --text-shadow-light: 0 1px 3px rgba(0, 0, 0, 0.3); /* For light text */
    --text-shadow-dark: 0 1px 3px rgba(255, 255, 255, 0.2); /* For dark text */

    /* Default to Light Theme */
    --theme-bg: #f8f9fa;
    --theme-text: #181823;
    --theme-primary: #3a5fcd; /* Slightly modern blue */
    --theme-secondary: #537FE7; /* Existing secondary, maybe adjust */
    --theme-accent: #045fab;
    --theme-border: rgba(0, 0, 0, 0.1);
    --theme-text-muted: #6c757d;
    --theme-text-on-primary: #ffffff; /* Text on primary background */

    /* Glass Effect */
    --glass-bg: rgba(255, 255, 255, 0.6); /* Light theme glass */
    --glass-blur: 10px;
    --glass-border: rgba(255, 255, 255, 0.3);
    --glass-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);


    /* Interaction Colors */
    --link-color: var(--theme-primary);
    --link-hover-color: color-mix(in srgb, var(--theme-primary) 80%, black);
    --button-bg: var(--theme-primary);
    --button-text: var(--theme-text-on-primary);

    --input-bg: #ffffff;
    --input-border: var(--theme-border);
    --input-focus-border: var(--theme-primary);

    /* Status Colors */
    --color-error: #dc3545;
    --color-success: #198754;
    --color-warning: #ffc107;
    --color-info: #0dcaf0;

    /* Scrollbar Theme */
    --scrollbar-track-color: color-mix(in srgb, var(--theme-bg) 90%, black);
    --scrollbar-thumb-color: var(--theme-secondary);
    --scrollbar-thumb-hover-color: var(--theme-primary);

    --secondary-color: #537FE7;
    --text-color: #000;


    --background-color: #f8f9fa;
    --dark-primary-color: #181823;
    --dark-secondary-color: #E9F8F9;
    --dark-background-color: #181823;
    --dark-text-color: #E9F8F9;

    --dark-nav-link-color: darkblue;
    --dark-nav-link-hover-color: #C0EEF2;
    --dark-toggle-bg-color: #181823;

    --toggle-bg-checked-color: var(--primary-color);

    --error-color: #9c1079;
    --fatal-error-color: #9c1010;
    --info-color: #107b9c;
    --susess-color: #109c41;
    --worning-color: #9c6b10;

    --decision-color: #75235e;
    --no-decision-color: #3f9f5a;

    --anti-text-clor: #fff;
    --light-acent: #045fab;
    --dark-acent: #011b33;

    --scrollbar-thumb-active-color: var(--dark-acent);

    --primary: var(--primary-color);
    --primary-hover:  var(--secondary-color);
    --primary-focus: rgba(var(--secondary-color), 0.25);
    --primary-inverse: var(--text-color);
    --theme-bg-sun: #ffffff;
    --theme-bg-light: #537FE7;

}

/* Dark Theme
:root[data-theme="light"],*/
:root[data-theme="dark"] {
    /* Oder data-theme="dark" wenn du das Attribut nutzt */
      --tb-color-background: #1f2937; /* gray-800 */
      --tb-color-text: #f3f4f6;       /* gray-100 */
      --tb-color-border: #4b5563;     /* gray-600 */

        --theme-bg-sun: #404060;
    --theme-bg-light: #181823;
    --theme-bg: #181823;
    --theme-text: #E9F8F9;
    --theme-primary: #6c8ee8; /* Lighter blue for dark mode */
    --theme-secondary: #7ba3ff;
    --theme-accent: #1a8cff;
    --theme-border: rgba(255, 255, 255, 0.15);
    --theme-text-muted: #adb5bd;
    --theme-text-on-primary: #181823; /* Dark text on light primary */

    /* Glass Effect */
    --glass-bg: rgba(2, 2, 3, 0.35); /* Dark theme glass */
    --glass-blur: 12px; /* Slightly more blur might look good */
    --glass-border: rgba(255, 255, 255, 0.1);
    --glass-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
    --text-shadow-current: var(--text-shadow-dark); /* Shadow for text on glass */

    /* Interaction Colors */
    --link-color: var(--theme-primary);
    --link-hover-color: color-mix(in srgb, var(--theme-primary) 80%, white);
    --button-bg: var(--theme-primary);
    --button-text: var(--theme-text-on-primary);
    --button-hover-bg: var(--link-hover-color);
    --input-bg: #2a2a3a; /* Darker input background */
    --input-border: var(--theme-border);
    --input-focus-border: var(--theme-primary);

    /* Scrollbar Theme */
    --scrollbar-track-color: color-mix(in srgb, var(--theme-bg) 80%, white);
    --scrollbar-thumb-color: var(--theme-secondary);
    --scrollbar-thumb-hover-color: var(--theme-primary);

    --primary-color: #181823;
    --secondary-color: #E9F8F9;
    --background-color: #f8f9fa;
    --text-color: #fff;

    --nav-link-hover-color: #fff;

    --nav-link-color: var(--dark-nav-link-color);


    --toggle-bg-color: var(--background-color);
    --toggle-bg-checked-color: var(--dark-text-color);

    --primary:  var(--primary-color);
    --primary-hover:  var(--secondary-color);
    --primary-focus: rgba(var(--secondary-color), 0.25);
    --primary-inverse: var(--text-color);

    --scrollbar-thumb-active-color: var(--dark-nav-link-color);
}



/* ==========================================================================
   2. Reset & Base Styles
   ========================================================================== */
*,
*::before,
*::after {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
    border-width: 0; /* Reset borders defined via attributes */
    border-style: solid;
    border-color: var(--theme-border); /* Default border color */
}

html {
    font-size: 110%; /* Slightly larger base size -> ~17.6px if browser default is 16px */
    line-height: var(--line-height-normal);
    scroll-behavior: smooth;
    /* Apply scrollbar styles globally (Firefox) */
    scrollbar-width: thin;
    scrollbar-color: var(--scrollbar-thumb-color) var(--scrollbar-track-color);
    height: 100%; /* Ensure html takes full height */
}

body {
    font-family: var(--font-family-base), ui-monospace;
    font-size: var(--font-size-base);
    font-weight: var(--font-weight-regular);
    color: var(--theme-text);
    background-color: transparent; /* Body is transparent, 3D scene is the true background */
    min-height: 100%;
    position: relative; /* Needed for absolute positioning of children like nav */
    transition: color var(--transition-medium), background-color var(--transition-medium);
    overflow-x: hidden; /* Prevent horizontal scroll on body */
}

body.dark-mode {
    --background-color: var(--dark-background-color);
    --text-color: var(--dark-text-color);
    --primary-color: var(--dark-primary-color);

    --primary:  var(--dark-primary-color);
    --primary-hover:  var(--dark-secondary-color);
    --primary-focus: rgba(var(--secondary-color), 0.25);
    --primary-inverse: var(--dark-text-color);

    --secondary-color: var(--dark-secondary-color);
    --nav-link-color: lightblue;

    --theme-bg: #181823;
    --theme-text: #E9F8F9;
    --theme-primary: #6c8ee8; /* Lighter blue for dark mode */
    --theme-secondary: #7ba3ff;
    --theme-accent: #1a8cff;
    --theme-border: rgba(255, 255, 255, 0.15);
    --theme-text-muted: #adb5bd;
    --theme-text-on-primary: #181823; /* Dark text on light primary */

    /* Glass Effect */
    --glass-bg: rgba(10, 10, 15, 0.85); /* Dark theme glass */
    --glass-blur: 12px; /* Slightly more blur might look good */
    --glass-border: rgba(255, 255, 255, 0.1);
    --glass-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
    --text-shadow-current: var(--text-shadow-dark); /* Shadow for text on glass */

    /* Interaction Colors */
    --link-color: var(--theme-primary);
    --link-hover-color: color-mix(in srgb, var(--theme-primary) 80%, white);
    --button-bg: var(--theme-primary);
    --button-text: var(--theme-text-on-primary);
    --button-hover-bg: var(--link-hover-color);
    --input-bg: #2a2a3a; /* Darker input background */
    --input-border: var(--theme-border);
    --input-focus-border: var(--theme-primary);

    /* Scrollbar Theme */
    --scrollbar-track-color: color-mix(in srgb, var(--theme-bg) 80%, white);
    --scrollbar-thumb-color: var(--theme-secondary);
    --scrollbar-thumb-hover-color: var(--theme-primary);

    --nav-link-hover-color: #fff;

    --toggle-bg-color: var(--background-color);
    --toggle-bg-checked-color: var(--dark-text-color);


    --scrollbar-thumb-active-color: var(--dark-nav-link-color);
}

/* Make sure 3D Scene is behind everything */
#threeDScene {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    z-index: var(--z-background);
}

/* Main Content Wrapper */
.Mcontent {
    position: relative;
    z-index: var(--z-content);
    width: 100%;
    height: 100vh; /* Full viewport height */
    overflow-x: hidden;
    overflow-y: auto; /* Allow scrolling */
    display: flex;
    justify-content: center;
    align-items: flex-start;
    /* MODIFIED: Reduced padding-top, just enough for fixed controls */
    padding-top: calc(var(--spacing) * 5); /* Adjust as needed based on toggle size */
    padding-bottom: var(--spacing);
    padding-left: var(--spacing); /* Add side padding if needed */
    padding-right: var(--spacing);
}

/* The actual content area */
.main-content { /* Ensure your <main> tag has this class! */
    max-width: 85vw;
    width: 100%;
    min-height: min-content;
    margin: 0 auto;
    padding: calc(var(--spacing) * 1.5) calc(var(--spacing) * 2);

    /* THE GLASS EFFECT */
    background-color: var(--glass-bg);
    backdrop-filter: blur(var(--glass-blur));
    -webkit-backdrop-filter: blur(var(--glass-blur));
    border-radius: var(--radius-lg);
    border: 1px solid var(--glass-border);
    box-shadow: var(--glass-shadow);
    text-shadow: var(--text-shadow-current);
}

/* Fallback (Keep as is) */
@supports not (backdrop-filter: blur(10px)) {
  .main-content, .widget, #Nav-Controls, .links-form, #cookie-banner, .modal-screen {
    background-color: color-mix(in srgb, var(--theme-bg) 95%, var(--theme-text));
  }
}

/* ==========================================================================
   3. Basic HTML Element Styling (Keep as is)
   ========================================================================== */
/* ... h1-h6, p, a, ul, ol, blockquote, code, pre, table, hr, img etc. ... */
h1, h2, h3, h4, h5, h6 {
    color: var(--text-color);
    margin-bottom: calc(var(--spacing) * 0.75);
    font-family: var(--font-family-base), ui-monospace;
    font-weight: var(--font-weight-semibold);
    line-height: var(--line-height-tight);
    letter-spacing: var(--letter-spacing-tight);
    text-wrap: balance;
}
h1 { font-size: var(--font-size-4xl); font-weight: var(--font-weight-bold); }
h2 { font-size: var(--font-size-3xl); font-weight: var(--font-weight-bold); }
h3 { font-size: var(--font-size-2xl); }
h4 { font-size: var(--font-size-xl); }
h5 { font-size: var(--font-size-lg); }
h6 { font-size: var(--font-size-base); font-weight: var(--font-weight-medium); }
p { margin-bottom: var(--spacing); line-height: var(--line-height-normal); max-width: 75ch; }
a { color: var(--link-color); text-decoration: none; transition: color var(--transition-fast); font-weight: var(--font-weight-medium); }
a:hover, a:focus { color: var(--link-hover-color); text-decoration: underline; text-decoration-thickness: 1.5px; text-underline-offset: 3px; }
a:focus-visible { outline: 2px solid var(--link-color); outline-offset: 2px; border-radius: var(--radius-sm); }
ul, ol { margin-bottom: var(--spacing); padding-left: calc(var(--spacing) * 1.5); }
li { margin-bottom: calc(var(--spacing) * 0.4); }
ul { list-style-type: disc; }
ol { list-style-type: decimal; }
li ul, li ol { margin-top: calc(var(--spacing) * 0.4); margin-bottom: 0; }
blockquote { margin: var(--spacing) 0 var(--spacing) calc(var(--spacing) * 1.5); padding: calc(var(--spacing) * 0.75) var(--spacing); border-left: 4px solid var(--theme-accent); background-color: color-mix(in srgb, var(--theme-bg) 80%, transparent); font-style: italic; color: var(--theme-text-muted); border-radius: 0 var(--radius-sm) var(--radius-sm) 0; }
blockquote p:last-child { margin-bottom: 0; }
code, pre { font-family: var(--font-family-mono), sans-serif; font-size: var(--font-size-sm); background-color: color-mix(in srgb, var(--theme-text) 5%, transparent); border-radius: var(--radius-sm); padding: 0.1em 0.3em; }
pre { padding: var(--spacing); margin-bottom: var(--spacing); overflow-x: auto; white-space: pre-wrap; border: 1px solid var(--theme-border); }
pre code { background-color: transparent; padding: 0; border-radius: 0; }
table { width: 100%; margin-bottom: var(--spacing); border-collapse: collapse; border: 1px solid var(--theme-border); font-size: var(--font-size-sm); }
th, td { padding: calc(var(--spacing) * 0.6) var(--spacing); text-align: left; border: 1px solid var(--theme-border); }
th { font-weight: var(--font-weight-semibold); background-color: color-mix(in srgb, var(--theme-text) 3%, transparent); }
tbody tr:nth-child(odd) { background-color: color-mix(in srgb, var(--theme-text) 1.5%, transparent); }
hr { border: 0; margin: calc(var(--spacing) * 1.5) 0; }
img, svg, video, canvas, picture { display: block; max-width: 100%; height: auto; }
figure { margin: 0 0 var(--spacing); }
figcaption { font-size: var(--font-size-sm); color: var(--theme-text-muted); margin-top: calc(var(--spacing) * 0.5); text-align: center; }


/* ==========================================================================
   4. Form Elements Styling (Keep as is)
   ========================================================================== */
/* ... label, input, textarea, select, button, form etc. ... */
label { display: block; margin-bottom: calc(var(--spacing) * 0.3); font-weight: var(--font-weight-medium); font-size: var(--font-size-sm); }
input, input[type="text"], input[type="email"], input[type="password"], input[type="number"], input[type="search"], input[type="tel"], input[type="url"], textarea, select { display: block; width: 100%; padding: calc(var(--spacing) * 0.6) var(--spacing); font-family: inherit; font-size: var(--font-size-base); line-height: var(--line-height-normal); color: var(--theme-text); background-color: var(--input-bg); background-clip: padding-box; border: 1px solid var(--input-border); border-radius: var(--radius-md); transition: border-color var(--transition-fast), box-shadow var(--transition-fast); appearance: none; margin-bottom: var(--spacing); }
input:focus, textarea:focus, select:focus { outline: none; border-color: var(--input-focus-border); box-shadow: 0 0 0 3px color-mix(in srgb, var(--theme-primary) 25%, transparent); }
textarea { resize: vertical; min-height: 120px; }
select { background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3e%3cpath fill='none' stroke='%23343a40' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='m2 5 6 6 6-6'/%3e%3c/svg%3e"); background-repeat: no-repeat; background-position: right var(--spacing) center; background-size: 16px 12px; padding-right: calc(var(--spacing) * 2.5); }
[data-theme="dark"] select { background-image: url("data:image/svg+xml,%3Csvg%20xmlns%3D'http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg'%20viewBox%3D'0%200%2016%2016'%3E%3Cpath%20fill%3D'none'%20stroke%3D'%23343a40'%20stroke-linecap%3D'round'%20stroke-linejoin%3D'round'%20stroke-width%3D'2'%20d%3D'm2%205%206%206%206-6'%2F%3E%3C%2Fsvg%3E"); }
input[type="checkbox"], input[type="radio"] { appearance: none; width: 1.1em; height: 1.1em; border: 1px solid var(--input-border); border-radius: var(--radius-sm); display: inline-block; vertical-align: middle; position: relative; cursor: pointer; margin-right: calc(var(--spacing) * 0.3); padding: 0; }
input[type="radio"] { border-radius: 50%; }
input[type="checkbox"]:checked, input[type="radio"]:checked { background-color: var(--theme-primary); border-color: var(--theme-primary); }
input[type="checkbox"]:checked::before { content: '✔'; color: var(--theme-text-on-primary); font-size: 0.8em; position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); line-height: 1; }
input[type="radio"]:checked::before { content: ''; display: block; width: 0.5em; height: 0.5em; background-color: var(--theme-text-on-primary); border-radius: 50%; position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); }
label > input[type="checkbox"], label > input[type="radio"] { margin-top: -0.1em; }
button, input[type="button"], input[type="submit"], input[type="reset"] { display: inline-block; padding: calc(var(--spacing) * 0.6) calc(var(--spacing) * 1.2); font-family: inherit; font-size: var(--font-size-base); font-weight: var(--font-weight-medium); line-height: var(--line-height-normal); color: var(--button-text); background-color: var(--button-bg); border: 1px solid transparent; border-radius: var(--radius-md); cursor: pointer; text-align: center; vertical-align: middle; user-select: none; transition: color var(--transition-fast), background-color var(--transition-fast), border-color var(--transition-fast), box-shadow var(--transition-fast); margin: 0 calc(var(--spacing) * 0.25) calc(var(--spacing) * 0.5) 0; }
button:hover, input[type="button"]:hover, input[type="submit"]:hover, input[type="reset"]:hover { background-color: var(--button-hover-bg); border-color: var(--button-hover-bg); text-decoration: none; }
button:focus-visible, input[type="button"]:focus-visible, input[type="submit"]:focus-visible, input[type="reset"]:focus-visible { outline: none; box-shadow: 0 0 0 3px color-mix(in srgb, var(--theme-primary) 35%, transparent); }
button:disabled, input:disabled { opacity: 0.65; cursor: not-allowed; }
form { display: flex; flex-direction: column; gap: calc(var(--spacing) * 0.8); margin: var(--spacing) 0; }
.form-container { display: flex; justify-content: center; gap: calc(var(--spacing) * 2); flex-wrap: wrap; }
.form { width: 100%; max-width: 400px; display: flex; flex-direction: column; gap: calc(var(--spacing) * 0.8); }
form button, form input[type="submit"] { align-self: center; min-width: 120px; }

/* ==========================================================================
   5. Component Styling (Navigation & Others)
   ========================================================================== */

/* REMOVED: header styling (now just a container) */
header {
    /* No background, shadow, or explicit height needed anymore */
    z-index: var(--z-nav-controls); /* Ensure it's interactive */
    width: auto; /* Only as wide as its content */
    padding: 0; /* Remove padding */
}

/* NEW: Fixed container for Nav Toggles */
#Nav-Controls {
    position: fixed;
    top: var(--spacing);
    left: var(--spacing);
    z-index: var(--z-nav-controls); /* Wichtig! */
    display: flex;
    align-items: center;
    gap: var(--spacing);
    padding: calc(var(--spacing) * 0.5);
    background-color: var(--glass-bg);
    backdrop-filter: blur(calc(var(--glass-blur) * 0.5));
    -webkit-backdrop-filter: blur(calc(var(--glass-blur) * 0.5));
    border-radius: var(--radius-lg);
    border: 1px solid var(--glass-border);
    box-shadow: var(--glass-shadow);
    pointer-events: auto;
}

#Nav-Main { /* Container für die Toggles */
    display: flex;
    align-items: center;
    gap: var(--spacing);
    list-style: none;
    padding: 0;
    margin: 0;
    position: relative; /* !! WICHTIG für die Positionierung des Dropdowns !! */
}
/* Menu Toggle Button (#links) */
#links.circle {
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: calc(var(--spacing) * 0.3);
    border-radius: 50%;
    transition: background-color var(--transition-fast), transform var(--transition-medium);
}

#links.circle:hover {
    background-color: color-mix(in srgb, var(--theme-text) 10%, transparent);
}
#links .material-symbols-outlined {
    font-size: var(--font-size-xl); /* Icon size */
    color: var(--theme-text);
    display: block;
}

/* Links Form (Dropdown Menu) */
.links-form { /* Oder nav.links-form wenn du das Tag änderst */
    position: absolute; /* Bezieht sich jetzt auf #Nav-Main */
    top: calc(100% + 10px); /* Position unterhalb von #Nav-Main (effektiv unter #Nav-Controls) */
    left: 0; /* Bündig mit #Nav-Main */
    z-index: var(--z-nav-dropdown); /* Sicherstellen, dass es über Controls ist */
    min-width: 200px;
    max-width: 300px;
    padding: calc(var(--spacing) * 0.75);
    list-style: none;
    background-color: var(--glass-bg);
    backdrop-filter: blur(var(--glass-blur));
    -webkit-backdrop-filter: blur(var(--glass-blur));
    border: 1px solid var(--glass-border);
    border-radius: var(--radius-md);
    box-shadow: var(--glass-shadow);
    color: var(--nav-link-color);
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px) scale(0.95);
    transform-origin: top left;
    transition: opacity var(--transition-fast), visibility var(--transition-fast), transform var(--transition-fast);
    text-align: left;
}

.links-form.is-active {
    opacity: 1;
    visibility: visible;
    transform: translateY(0) scale(1);
}

.links-form hr {
    border-top: 1px solid var(--theme-border); /* Verwende Theme-Border */
    margin: calc(var(--spacing) * 0.5) 0; /* Vertikaler Abstand */
}

.links-form ul { padding: 0; margin: 0; list-style: none; }
.links-form li { margin: 0; }
.links-form a {
    display: block;
    padding: calc(var(--spacing) * 0.7) var(--spacing); /* Increase padding */
    color: var(--nav-link-color);
    text-decoration: none;
    border-radius: var(--radius-sm);
    transition: background-color var(--transition-fast), color var(--transition-fast);
    white-space: nowrap;
    background-color: transparent;
    font-weight: var(--font-weight-regular); /* Normal weight */
}

.links-form a:hover,
.links-form a:focus {
    background-color: var(--theme-primary);
    color: var(--nav-link-hover-color);
    text-decoration: none;
}
.links-form a:focus-visible { /* Add focus style */
    outline: 2px solid var(--theme-primary);
    outline-offset: 1px;
}

/* Dark Mode Toggle (Styles should be mostly correct now) */
#darkModeToggle { /* The actual checkbox */
    opacity: 0;
    width: 0;
    height: 0;
    position: absolute;
    /* display: none; REMOVE THIS if you use the checkbox state */
}
#toggleLabel { /* The label acting as the button */
    cursor: pointer;
    padding: calc(var(--spacing) * 0.3); /* Click area */
    display: inline-flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: background-color var(--transition-fast), transform var(--transition-medium); /* Keep existing transform */
}
#toggleLabel:hover {
    background-color: color-mix(in srgb, var(--theme-text) 10%, transparent);
}
#toggleLabel .material-symbols-outlined {
    font-size: var(--font-size-xl); /* Icon size */
    color: var(--theme-text);
    display: block;
    transition: transform var(--transition-medium); /* Icon transition */
    scale: 1.6;
}
/* Rotate icon on toggle (example using :checked) */
#darkModeToggle:checked + #toggleLabel {
    /* Optional: visual feedback on label itself */
    /* background-color: var(--theme-primary); */
}
#darkModeToggle:checked + #toggleLabel .material-symbols-outlined {
    transform: rotate(180deg); /* Keep rotation */
    /* color: var(--theme-text-on-primary); */ /* If label background changes */
}

/* Cards, Widgets, Cookie Banner, Modal, Overlay, Markdown (Keep as is) */
/* ... .card, .widget, #cookie-banner, #cookie-modal, #overlay, .markdown ... */
.card { background-color: color-mix(in srgb, var(--glass-bg) 80%, transparent); border: 1px solid var(--glass-border); border-radius: var(--radius-md); padding: calc(var(--spacing) * 1.2); margin-bottom: var(--spacing); box-shadow: 0 2px 8px rgba(0, 0, 0, 0.07); transition: transform var(--transition-fast), box-shadow var(--transition-fast); color: inherit; text-shadow: var(--text-shadow-current); }
.card:hover { transform: translateY(-3px); box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1); }
.card-title { font-size: var(--font-size-lg); font-weight: var(--font-weight-semibold); color: inherit; margin-bottom: calc(var(--spacing) * 0.5); }
.card-content { color: inherit; line-height: var(--line-height-normal); font-size: var(--font-size-base); }
.card-content p:last-child { margin-bottom: 0; }
.widget { position: absolute; display: flex; flex-direction: column; align-items: stretch; background-color: var(--glass-bg); backdrop-filter: blur(var(--glass-blur)); -webkit-backdrop-filter: blur(var(--glass-blur)); border: 1px solid var(--glass-border); border-radius: var(--radius-md); box-shadow: var(--glass-shadow); padding: var(--spacing); padding-top: calc(var(--spacing) * 1.8); animation: widget-fadeIn 0.3s ease-out; resize: both; overflow: hidden; min-width: 200px; min-height: 100px; max-height: 80vh; height: fit-content; color: var(--theme-text); text-shadow: var(--text-shadow-current); }
.widget-from { position: absolute; top: 5px; left: var(--spacing); color: var(--theme-text-muted); padding: 2px 6px; border-radius: var(--radius-sm); font-size: var(--font-size-xs); font-weight: var(--font-weight-medium); z-index: 2; }
.widget-content { padding: 0; margin: 0; overflow-y: auto; flex-grow: 1; scrollbar-width: thin; scrollbar-color: var(--scrollbar-thumb-color) var(--scrollbar-track-color); }
.widget-content::-webkit-scrollbar { width: var(--scrollbar-width); height: var(--scrollbar-height); }
.widget-content::-webkit-scrollbar-track { background: transparent; }
.widget-content::-webkit-scrollbar-thumb { background: var(--scrollbar-thumb-color); border-radius: calc(var(--scrollbar-width) / 2); border: 2px solid transparent; }
.widget-content::-webkit-scrollbar-thumb:hover { background: var(--scrollbar-thumb-hover-color); }
.widget-close-button, .widget-action-button { position: absolute; top: 5px; background: none; border: none; padding: 4px; cursor: pointer; color: var(--theme-text-muted); transition: color var(--transition-fast); z-index: 2; display: flex; align-items: center; justify-content: center; border-radius: 50%; }
.widget-close-button:hover, .widget-action-button:hover { color: var(--theme-text); background-color: color-mix(in srgb, var(--theme-text) 10%, transparent); }
.widget-close-button { right: 5px; }
.widget-action-button { right: 30px; }
.widget-resize-handle { position: absolute; bottom: 2px; right: 2px; width: 16px; height: 16px; cursor: nwse-resize; z-index: 3; opacity: 0.4; transition: opacity var(--transition-fast); }
.widget-resize-handle::after { content: ''; position: absolute; bottom: 3px; right: 3px; width: 6px; height: 6px; border-bottom: 2px solid currentColor; border-right: 2px solid currentColor; transform: rotate(45deg); }
.widget:hover .widget-resize-handle { opacity: 1; }
#cookie-banner { position: fixed; bottom: var(--spacing); right: var(--spacing); z-index: var(--z-cookie-banner); max-width: 350px; padding: var(--spacing); background-color: var(--glass-bg); backdrop-filter: blur(var(--glass-blur)); -webkit-backdrop-filter: blur(var(--glass-blur)); border: 1px solid var(--glass-border); border-radius: var(--radius-md); box-shadow: var(--glass-shadow); color: var(--theme-text); text-shadow: var(--text-shadow-current); font-size: var(--font-size-sm); }
#cookie-banner p { margin-bottom: calc(var(--spacing) * 0.75); max-width: none; }
#cookie-banner a { font-weight: var(--font-weight-semibold); }
#cookie-banner a:hover { text-decoration: underline; }
.cookie-actions { display: flex; gap: var(--spacing); justify-content: flex-end; }
#close-banner { background: none; border: none; color: var(--theme-text-muted); cursor: pointer; padding: 0; font-size: 1.5em; position: absolute; top: calc(var(--spacing) / 2); right: calc(var(--spacing) / 2); line-height: 1; }
#close-banner:hover { color: var(--theme-text); }
#cookie-modal { display: none; position: fixed; inset: 0; background: rgba(0,0,0,0.7); z-index: var(--z-modal); display: flex; align-items: center; justify-content: center; padding: var(--spacing); opacity: 0; visibility: hidden; transition: opacity var(--transition-medium), visibility var(--transition-medium); }
#cookie-modal.is-visible { opacity: 1; visibility: visible; }
.modal-screen { display: none; width: 100%; max-width: 550px; margin: 0; padding: calc(var(--spacing) * 1.5); border-radius: var(--radius-lg); position: relative; background-color: var(--glass-bg); backdrop-filter: blur(var(--glass-blur)); -webkit-backdrop-filter: blur(var(--glass-blur)); border: 1px solid var(--glass-border); box-shadow: var(--glass-shadow); color: var(--theme-text); text-shadow: var(--text-shadow-current); max-height: 90vh; overflow-y: auto; }
.modal-screen.active { display: block; }
.modal-screen h2 { margin-top: 0; }
#overlay { position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0, 0, 0, 0.5); z-index: var(--z-overlay); opacity: 0; visibility: hidden; transition: opacity var(--transition-medium), visibility var(--transition-medium); backdrop-filter: blur(3px); -webkit-backdrop-filter: blur(3px); }
#overlay.active { opacity: 1; visibility: visible; }
.markdown { text-align: left; background: color-mix(in srgb, var(--theme-bg) 90%, transparent); padding: var(--spacing); border-radius: var(--radius-md); border: 1px solid var(--theme-border); font-size: var(--font-size-base); max-width: 100%; color: inherit; text-shadow: none; }
.markdown > *:first-child { margin-top: 0; }
.markdown > *:last-child { margin-bottom: 0; }
.markdown h1 { font-size: var(--font-size-2xl); }
.markdown h2 { font-size: var(--font-size-xl); }

/* ==========================================================================
   6. Utility Classes & Responsiveness (Keep as is)
   ========================================================================== */
/* ... .text-*, .font-*, .none, .hidden, media queries ... */
.text-small { font-size: var(--font-size-sm); }
.text-large { font-size: var(--font-size-lg); }
.text-display { font-size: var(--font-size-4xl); line-height: var(--line-height-tight); font-weight: var(--font-weight-bold); letter-spacing: var(--letter-spacing-tight); }
.font-light { font-weight: var(--font-weight-light); }
.font-regular { font-weight: var(--font-weight-regular); }
.font-medium { font-weight: var(--font-weight-medium); }
.font-semibold { font-weight: var(--font-weight-semibold); }
.font-bold { font-weight: var(--font-weight-bold); }
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }
.none, .hidden { display: none !important; }
@media screen and (max-width: 1024px) { html { font-size: 105%; } .main-content { max-width: 90vw; padding: calc(var(--spacing) * 1.2) calc(var(--spacing) * 1.5); } }
@media screen and (max-width: 767px) { html { font-size: 100%; } .main-content { max-width: 95vw; padding: var(--spacing); border-radius: var(--radius-md); } .Mcontent { padding-top: calc(var(--spacing) * 6); } /* Adjust padding for mobile */ h1 { font-size: var(--font-size-3xl); } h2 { font-size: var(--font-size-2xl); } h3 { font-size: var(--font-size-xl); } .form-container { flex-direction: column; gap: var(--spacing); } .form { max-width: 100%; } button, input[type="button"], input[type="submit"], input[type="reset"] { padding: calc(var(--spacing) * 0.7) calc(var(--spacing) * 1.4); width: 100%; max-width: 300px; margin-right: 0; } form button, form input[type="submit"] { align-self: stretch; max-width: none; } }
input[type="color"] {
    display: inline-block;
    width: 3rem;
    height: 3rem;
    padding: 0;
    margin-bottom: var(--spacing);
    background-color: var(--input-bg);
    border: 2px solid var(--input-border);
    border-radius: var(--radius-md);
    cursor: pointer;
    transition: border-color var(--transition-fast), box-shadow var(--transition-fast), transform var(--transition-fast);
    appearance: none;
    overflow: hidden;
    vertical-align: middle;
    box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.1);
}

input[type="color"]::-webkit-color-swatch-wrapper {
    padding: 0;
    border: none;
    border-radius: calc(var(--radius-md) - 2px);
}

input[type="color"]::-webkit-color-swatch {
    border: none;
    border-radius: calc(var(--radius-md) - 2px);
    box-shadow: none;
}

input[type="color"]::-moz-color-swatch {
    border: none;
    border-radius: calc(var(--radius-md) - 2px);
}

input[type="color"]:focus {
    outline: none;
    border-color: var(--input-focus-border);
    box-shadow: 0 0 0 3px color-mix(in srgb, var(--theme-primary) 25%, transparent),
                inset 0 1px 3px rgba(0, 0, 0, 0.1);
    transform: scale(1.05);
}

input[type="color"]:hover:not(:focus) {
    border-color: var(--theme-primary);
    transform: scale(1.02);
}

input[type="color"]:disabled {
    opacity: 0.65;
    cursor: not-allowed;
    transform: none;
}

/* Responsive size variants with media queries */
input[type="color"].large {
    width: 4rem;
    height: 4rem;
}

input[type="color"].small {
    width: 2rem;
    height: 2rem;
}

/* Auto-responsive sizing based on viewport */
@media screen and (max-width: 1024px) {
    input[type="color"] {
        width: 2.75rem;
        height: 2.75rem;
    }

    input[type="color"].large {
        width: 3.5rem;
        height: 3.5rem;
    }

    input[type="color"].small {
        width: 1.75rem;
        height: 1.75rem;
    }
}

@media screen and (max-width: 767px) {
    input[type="color"] {
        width: 2.5rem;
        height: 2.5rem;
    }

    input[type="color"].large {
        width: 3rem;
        height: 3rem;
    }

    input[type="color"].small {
        width: 1.5rem;
        height: 1.5rem;
    }
}
/* ==========================================================================
   7. Scrollbar Styles (Webkit) & Icons (Keep as is)
   ========================================================================== */
/* ... ::-webkit-scrollbar*, .hide-scrollbar, .material-symbols-outlined ... */
::-webkit-scrollbar { width: var(--scrollbar-width); height: var(--scrollbar-height); }
::-webkit-scrollbar-track { background: var(--scrollbar-track-color); border-radius: calc(var(--scrollbar-width) / 2); }
::-webkit-scrollbar-thumb { background: var(--scrollbar-thumb-color); border-radius: calc(var(--scrollbar-width) / 2); border: 2px solid var(--scrollbar-track-color); transition: background-color var(--transition-fast); }
::-webkit-scrollbar-thumb:hover { background: var(--scrollbar-thumb-hover-color); }
::-webkit-scrollbar-corner { background: var(--scrollbar-track-color); }
.hide-scrollbar { -ms-overflow-style: none; scrollbar-width: none; }
.hide-scrollbar::-webkit-scrollbar { display: none; }
.material-symbols-outlined { font-variation-settings: 'FILL' var(--FILL, 0), 'wght' var(--wght, 300), 'GRAD' var(--GRAD, 0), 'opsz' var(--opsz, 24); vertical-align: middle; line-height: 1; flex-shrink: 0; }

/* ==========================================================================
   8. Animations (Keep as is)
   ========================================================================== */
@keyframes fadeInFromLeft { 0% { opacity: 0; transform: translateX(-10px); } 100% { opacity: 1; transform: translateX(0); } }
@keyframes fadeOutToLeft { 0% { opacity: 1; transform: translateX(0); } 100% { opacity: 0; transform: translateX(-10px); } }
@keyframes widget-fadeIn { from { opacity: 0; transform: translateY(10px); } to { opacity: 1; transform: translateY(0); } }
.fade-inH { animation: fadeInFromLeft 0.5s ease forwards; }
.fade-outH { animation: fadeOutToLeft 0.5s ease forwards; }


/* Select specific styling for dropdown arrow */
select {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3e%3cpath fill='none' stroke='%23343a40' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='m2 5 6 6 6-6'/%3e%3c/svg%3e");
    background-repeat: no-repeat;
    background-position: right var(--spacing) center;
    background-size: 16px 12px;
    padding-right: calc(var(--spacing) * 2.5); /* Space for arrow */
}
/* Dark mode arrow */
[data-theme="dark"] select {
     background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3e%3cpath fill='none' stroke='%23dee2e6' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='m2 5 6 6 6-6'/%3e%3c/svg%3e");
}


/* Ensure Material Symbols are loaded if not already globally available for icons */
@import url('https://fonts.googleapis.com/css2?family=Material+Symbols+Outlined:opsz,wght,FILL,GRAD@20..48,100..700,0..1,-50..200');

.material-symbols-outlined {
  font-variation-settings:
  'FILL' 0,
  'wght' 400,
  'GRAD' 0,
  'opsz' 24;
  font-family: 'Material Symbols Outlined';
  font-weight: normal;
  font-style: normal;
  font-size: 24px;
  line-height: 1;
  letter-spacing: normal;
  text-transform: none;
  display: inline-block;
  white-space: nowrap;
  word-wrap: normal;
  direction: ltr;
  -webkit-font-feature-settings: 'liga';
  -webkit-font-smoothing: antialiased;
}
