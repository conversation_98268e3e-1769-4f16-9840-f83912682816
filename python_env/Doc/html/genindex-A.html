
<!DOCTYPE html>

<html lang="en" data-content_root="./">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Index &#8212; Python 3.12.9 documentation</title><meta name="viewport" content="width=device-width, initial-scale=1.0">
    
    <link rel="stylesheet" type="text/css" href="_static/pygments.css?v=b86133f3" />
    <link rel="stylesheet" type="text/css" href="_static/pydoctheme.css?v=23252803" />
    <link id="pygments_dark_css" media="(prefers-color-scheme: dark)" rel="stylesheet" type="text/css" href="_static/pygments_dark.css?v=5349f25f" />
    
    <script src="_static/documentation_options.js?v=9c91bd08"></script>
    <script src="_static/doctools.js?v=9bcbadda"></script>
    <script src="_static/sphinx_highlight.js?v=dc90522c"></script>
    
    <script src="_static/sidebar.js"></script>
    
    <link rel="search" type="application/opensearchdescription+xml"
          title="Search within Python 3.12.9 documentation"
          href="_static/opensearch.xml"/>
    <link rel="author" title="About these documents" href="about.html" />
    <link rel="index" title="Index" href="genindex.html" />
    <link rel="search" title="Search" href="search.html" />
    <link rel="copyright" title="Copyright" href="copyright.html" />
    
    <link rel="canonical" href="https://docs.python.org/3/genindex-A.html" />
    
      
    

    
    <style>
      @media only screen {
        table.full-width-table {
            width: 100%;
        }
      }
    </style>
<link rel="stylesheet" href="_static/pydoctheme_dark.css" media="(prefers-color-scheme: dark)" id="pydoctheme_dark_css">
    <link rel="shortcut icon" type="image/png" href="_static/py.svg" />
            <script type="text/javascript" src="_static/copybutton.js"></script>
            <script type="text/javascript" src="_static/menu.js"></script>
            <script type="text/javascript" src="_static/search-focus.js"></script>
            <script type="text/javascript" src="_static/themetoggle.js"></script> 
            <script type="text/javascript" src="_static/rtd_switcher.js"></script>
            <meta name="readthedocs-addons-api-version" content="1">

  </head>
<body>
<div class="mobile-nav">
    <input type="checkbox" id="menuToggler" class="toggler__input" aria-controls="navigation"
           aria-pressed="false" aria-expanded="false" role="button" aria-label="Menu" />
    <nav class="nav-content" role="navigation">
        <label for="menuToggler" class="toggler__label">
            <span></span>
        </label>
        <span class="nav-items-wrapper">
            <a href="https://www.python.org/" class="nav-logo">
                <img src="_static/py.svg" alt="Python logo"/>
            </a>
            <span class="version_switcher_placeholder"></span>
            <form role="search" class="search" action="search.html" method="get">
                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" class="search-icon">
                    <path fill-rule="nonzero" fill="currentColor" d="M15.5 14h-.79l-.28-.27a6.5 6.5 0 001.48-5.34c-.47-2.78-2.79-5-5.59-5.34a6.505 6.505 0 00-7.27 7.27c.34 2.8 2.56 5.12 5.34 5.59a6.5 6.5 0 005.34-1.48l.27.28v.79l4.25 4.25c.41.41 1.08.41 1.49 0 .41-.41.41-1.08 0-1.49L15.5 14zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z"></path>
                </svg>
                <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" />
                <input type="submit" value="Go"/>
            </form>
        </span>
    </nav>
    <div class="menu-wrapper">
        <nav class="menu" role="navigation" aria-label="main navigation">
            <div class="language_switcher_placeholder"></div>
            
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label>
        </nav>
    </div>
</div>

  
    <div class="related" role="navigation" aria-label="Related">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="genindex.html" title="General Index"
             accesskey="I">index</a></li>
        <li class="right" >
          <a href="py-modindex.html" title="Python Module Index"
             >modules</a> |</li>

          <li><img src="_static/py.svg" alt="Python logo" style="vertical-align: middle; margin-top: -1px"/></li>
          <li><a href="https://www.python.org/">Python</a> &#187;</li>
          <li class="switchers">
            <div class="language_switcher_placeholder"></div>
            <div class="version_switcher_placeholder"></div>
          </li>
          <li>
              
          </li>
    <li id="cpython-language-and-version">
      <a href="index.html">3.12.9 Documentation</a> &#187;
    </li>

        <li class="nav-item nav-item-this"><a href="">Index</a></li>
                <li class="right">
                    

    <div class="inline-search" role="search">
        <form class="inline-search" action="search.html" method="get">
          <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" id="search-box" />
          <input type="submit" value="Go" />
        </form>
    </div>
                     |
                </li>
            <li class="right">
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label> |</li>
            
      </ul>
    </div>    

    <div class="document">
      <div class="documentwrapper">
        <div class="bodywrapper">
          <div class="body" role="main">
            


<h1 id="index">Index &#x2013; A</h1>

<table style="width: 100%" class="indextable"><tr>
  <td style="width: 33%; vertical-align: top;"><ul>
      <li><a href="library/re.html#re.A">A (in module re)</a>
</li>
      <li><a href="library/aifc.html#index-2">A-LAW</a>, <a href="library/sndhdr.html#index-0">[1]</a>
</li>
      <li><a href="library/audioop.html#index-1">a-LAW</a>
</li>
      <li><a href="library/binascii.html#binascii.a2b_base64">a2b_base64() (in module binascii)</a>
</li>
      <li><a href="library/binascii.html#binascii.a2b_hex">a2b_hex() (in module binascii)</a>
</li>
      <li><a href="library/binascii.html#binascii.a2b_qp">a2b_qp() (in module binascii)</a>
</li>
      <li><a href="library/binascii.html#binascii.a2b_uu">a2b_uu() (in module binascii)</a>
</li>
      <li><a href="library/base64.html#base64.a85decode">a85decode() (in module base64)</a>
</li>
      <li><a href="library/base64.html#base64.a85encode">a85encode() (in module base64)</a>
</li>
      <li><a href="library/curses.html#curses.A_ALTCHARSET">A_ALTCHARSET (in module curses)</a>
</li>
      <li><a href="library/curses.html#curses.A_ATTRIBUTES">A_ATTRIBUTES (in module curses)</a>
</li>
      <li><a href="library/curses.html#curses.A_BLINK">A_BLINK (in module curses)</a>
</li>
      <li><a href="library/curses.html#curses.A_BOLD">A_BOLD (in module curses)</a>
</li>
      <li><a href="library/curses.html#curses.A_CHARTEXT">A_CHARTEXT (in module curses)</a>
</li>
      <li><a href="library/curses.html#curses.A_COLOR">A_COLOR (in module curses)</a>
</li>
      <li><a href="library/curses.html#curses.A_DIM">A_DIM (in module curses)</a>
</li>
      <li><a href="library/curses.html#curses.A_HORIZONTAL">A_HORIZONTAL (in module curses)</a>
</li>
      <li><a href="library/curses.html#curses.A_INVIS">A_INVIS (in module curses)</a>
</li>
      <li><a href="library/curses.html#curses.A_ITALIC">A_ITALIC (in module curses)</a>
</li>
      <li><a href="library/curses.html#curses.A_LEFT">A_LEFT (in module curses)</a>
</li>
      <li><a href="library/curses.html#curses.A_LOW">A_LOW (in module curses)</a>
</li>
      <li><a href="library/curses.html#curses.A_NORMAL">A_NORMAL (in module curses)</a>
</li>
      <li><a href="library/curses.html#curses.A_PROTECT">A_PROTECT (in module curses)</a>
</li>
      <li><a href="library/curses.html#curses.A_REVERSE">A_REVERSE (in module curses)</a>
</li>
      <li><a href="library/curses.html#curses.A_RIGHT">A_RIGHT (in module curses)</a>
</li>
      <li><a href="library/curses.html#curses.A_STANDOUT">A_STANDOUT (in module curses)</a>
</li>
      <li><a href="library/curses.html#curses.A_TOP">A_TOP (in module curses)</a>
</li>
      <li><a href="library/curses.html#curses.A_UNDERLINE">A_UNDERLINE (in module curses)</a>
</li>
      <li><a href="library/curses.html#curses.A_VERTICAL">A_VERTICAL (in module curses)</a>
</li>
      <li>
    abc

      <ul>
        <li><a href="library/abc.html#module-abc">module</a>
</li>
      </ul></li>
      <li><a href="library/abc.html#abc.ABC">ABC (class in abc)</a>
</li>
      <li><a href="library/abc.html#abc.ABCMeta">ABCMeta (class in abc)</a>
</li>
      <li><a href="library/locale.html#locale.ABDAY_1">ABDAY_1 (in module locale)</a>
</li>
      <li><a href="library/locale.html#locale.ABDAY_2">ABDAY_2 (in module locale)</a>
</li>
      <li><a href="library/locale.html#locale.ABDAY_3">ABDAY_3 (in module locale)</a>
</li>
      <li><a href="library/locale.html#locale.ABDAY_4">ABDAY_4 (in module locale)</a>
</li>
      <li><a href="library/locale.html#locale.ABDAY_5">ABDAY_5 (in module locale)</a>
</li>
      <li><a href="library/locale.html#locale.ABDAY_6">ABDAY_6 (in module locale)</a>
</li>
      <li><a href="library/locale.html#locale.ABDAY_7">ABDAY_7 (in module locale)</a>
</li>
      <li><a href="library/sys.html#sys.abiflags">abiflags (in module sys)</a>
</li>
      <li><a href="library/locale.html#locale.ABMON_1">ABMON_1 (in module locale)</a>
</li>
      <li><a href="library/locale.html#locale.ABMON_10">ABMON_10 (in module locale)</a>
</li>
      <li><a href="library/locale.html#locale.ABMON_11">ABMON_11 (in module locale)</a>
</li>
      <li><a href="library/locale.html#locale.ABMON_12">ABMON_12 (in module locale)</a>
</li>
      <li><a href="library/locale.html#locale.ABMON_2">ABMON_2 (in module locale)</a>
</li>
      <li><a href="library/locale.html#locale.ABMON_3">ABMON_3 (in module locale)</a>
</li>
      <li><a href="library/locale.html#locale.ABMON_4">ABMON_4 (in module locale)</a>
</li>
      <li><a href="library/locale.html#locale.ABMON_5">ABMON_5 (in module locale)</a>
</li>
      <li><a href="library/locale.html#locale.ABMON_6">ABMON_6 (in module locale)</a>
</li>
      <li><a href="library/locale.html#locale.ABMON_7">ABMON_7 (in module locale)</a>
</li>
      <li><a href="library/locale.html#locale.ABMON_8">ABMON_8 (in module locale)</a>
</li>
      <li><a href="library/locale.html#locale.ABMON_9">ABMON_9 (in module locale)</a>
</li>
      <li><a href="c-api/sys.html#index-2">abort (C function)</a>
</li>
      <li><a href="library/tkinter.messagebox.html#tkinter.messagebox.ABORT">ABORT (in module tkinter.messagebox)</a>
</li>
      <li><a href="library/asyncio-sync.html#asyncio.Barrier.abort">abort() (asyncio.Barrier method)</a>

      <ul>
        <li><a href="library/asyncio-protocol.html#asyncio.DatagramTransport.abort">(asyncio.DatagramTransport method)</a>
</li>
        <li><a href="library/asyncio-protocol.html#asyncio.WriteTransport.abort">(asyncio.WriteTransport method)</a>
</li>
        <li><a href="library/ftplib.html#ftplib.FTP.abort">(ftplib.FTP method)</a>
</li>
        <li><a href="library/os.html#os.abort">(in module os)</a>
</li>
        <li><a href="library/threading.html#threading.Barrier.abort">(threading.Barrier method)</a>
</li>
      </ul></li>
      <li><a href="library/tkinter.messagebox.html#tkinter.messagebox.ABORTRETRYIGNORE">ABORTRETRYIGNORE (in module tkinter.messagebox)</a>
</li>
      <li><a href="library/curses.panel.html#curses.panel.Panel.above">above() (curses.panel.Panel method)</a>
</li>
      <li><a href="library/subprocess.html#subprocess.ABOVE_NORMAL_PRIORITY_CLASS">ABOVE_NORMAL_PRIORITY_CLASS (in module subprocess)</a>
</li>
      <li>
    abs

      <ul>
        <li><a href="c-api/number.html#index-2">built-in function</a>, <a href="reference/datamodel.html#index-105">[1]</a>
</li>
      </ul></li>
      <li>
    abs()

      <ul>
        <li><a href="library/functions.html#abs">built-in function</a>
</li>
      </ul></li>
      <li><a href="library/decimal.html#decimal.Context.abs">abs() (decimal.Context method)</a>

      <ul>
        <li><a href="library/operator.html#operator.abs">(in module operator)</a>
</li>
      </ul></li>
      <li><a href="library/pathlib.html#pathlib.Path.absolute">absolute() (pathlib.Path method)</a>
</li>
      <li><a href="library/tarfile.html#tarfile.AbsoluteLinkError">AbsoluteLinkError</a>
</li>
      <li><a href="library/tarfile.html#tarfile.AbsolutePathError">AbsolutePathError</a>
</li>
      <li><a href="library/os.path.html#os.path.abspath">abspath() (in module os.path)</a>
</li>
      <li><a href="glossary.html#term-abstract-base-class"><strong>abstract base class</strong></a>
</li>
      <li><a href="library/contextlib.html#contextlib.AbstractAsyncContextManager">AbstractAsyncContextManager (class in contextlib)</a>
</li>
      <li><a href="library/urllib.request.html#urllib.request.AbstractBasicAuthHandler">AbstractBasicAuthHandler (class in urllib.request)</a>
</li>
      <li><a href="library/asyncio-policy.html#asyncio.AbstractChildWatcher">AbstractChildWatcher (class in asyncio)</a>
</li>
      <li><a href="library/abc.html#abc.abstractclassmethod">abstractclassmethod() (in module abc)</a>
</li>
      <li><a href="library/contextlib.html#contextlib.AbstractContextManager">AbstractContextManager (class in contextlib)</a>
</li>
      <li><a href="library/urllib.request.html#urllib.request.AbstractDigestAuthHandler">AbstractDigestAuthHandler (class in urllib.request)</a>
</li>
      <li><a href="library/asyncio-eventloop.html#asyncio.AbstractEventLoop">AbstractEventLoop (class in asyncio)</a>
</li>
      <li><a href="library/asyncio-policy.html#asyncio.AbstractEventLoopPolicy">AbstractEventLoopPolicy (class in asyncio)</a>
</li>
      <li><a href="library/abc.html#abc.abstractmethod">abstractmethod() (in module abc)</a>
</li>
      <li><a href="library/abc.html#abc.abstractproperty">abstractproperty() (in module abc)</a>
</li>
      <li><a href="library/typing.html#typing.AbstractSet">AbstractSet (class in typing)</a>
</li>
      <li><a href="library/abc.html#abc.abstractstaticmethod">abstractstaticmethod() (in module abc)</a>
</li>
      <li><a href="library/multiprocessing.html#multiprocessing.connection.Listener.accept">accept() (multiprocessing.connection.Listener method)</a>

      <ul>
        <li><a href="library/socket.html#socket.socket.accept">(socket.socket method)</a>
</li>
      </ul></li>
      <li><a href="library/os.html#os.access">access() (in module os)</a>
</li>
      <li><a href="library/itertools.html#itertools.accumulate">accumulate() (in module itertools)</a>
</li>
      <li><a href="library/curses.ascii.html#curses.ascii.ACK">ACK (in module curses.ascii)</a>
</li>
      <li><a href="reference/expressions.html#agen.aclose">aclose() (agen method)</a>

      <ul>
        <li><a href="library/contextlib.html#contextlib.AsyncExitStack.aclose">(contextlib.AsyncExitStack method)</a>
</li>
      </ul></li>
      <li><a href="library/contextlib.html#contextlib.aclosing">aclosing() (in module contextlib)</a>
</li>
      <li><a href="library/cmath.html#cmath.acos">acos() (in module cmath)</a>

      <ul>
        <li><a href="library/math.html#math.acos">(in module math)</a>
</li>
      </ul></li>
      <li><a href="library/cmath.html#cmath.acosh">acosh() (in module cmath)</a>

      <ul>
        <li><a href="library/math.html#math.acosh">(in module math)</a>
</li>
      </ul></li>
      <li><a href="library/_thread.html#thread.lock.acquire">acquire() (_thread.lock method)</a>

      <ul>
        <li><a href="library/asyncio-sync.html#asyncio.Condition.acquire">(asyncio.Condition method)</a>
</li>
        <li><a href="library/asyncio-sync.html#asyncio.Lock.acquire">(asyncio.Lock method)</a>
</li>
        <li><a href="library/asyncio-sync.html#asyncio.Semaphore.acquire">(asyncio.Semaphore method)</a>
</li>
        <li><a href="library/logging.html#logging.Handler.acquire">(logging.Handler method)</a>
</li>
        <li><a href="library/multiprocessing.html#multiprocessing.Lock.acquire">(multiprocessing.Lock method)</a>
</li>
        <li><a href="library/multiprocessing.html#multiprocessing.RLock.acquire">(multiprocessing.RLock method)</a>
</li>
        <li><a href="library/threading.html#threading.Condition.acquire">(threading.Condition method)</a>
</li>
        <li><a href="library/threading.html#threading.Lock.acquire">(threading.Lock method)</a>
</li>
        <li><a href="library/threading.html#threading.RLock.acquire">(threading.RLock method)</a>
</li>
        <li><a href="library/threading.html#threading.Semaphore.acquire">(threading.Semaphore method)</a>
</li>
      </ul></li>
      <li><a href="library/curses.html#curses.ACS_BBSS">ACS_BBSS (in module curses)</a>
</li>
      <li><a href="library/curses.html#curses.ACS_BLOCK">ACS_BLOCK (in module curses)</a>
</li>
      <li><a href="library/curses.html#curses.ACS_BOARD">ACS_BOARD (in module curses)</a>
</li>
      <li><a href="library/curses.html#curses.ACS_BSBS">ACS_BSBS (in module curses)</a>
</li>
      <li><a href="library/curses.html#curses.ACS_BSSB">ACS_BSSB (in module curses)</a>
</li>
      <li><a href="library/curses.html#curses.ACS_BSSS">ACS_BSSS (in module curses)</a>
</li>
      <li><a href="library/curses.html#curses.ACS_BTEE">ACS_BTEE (in module curses)</a>
</li>
      <li><a href="library/curses.html#curses.ACS_BULLET">ACS_BULLET (in module curses)</a>
</li>
      <li><a href="library/curses.html#curses.ACS_CKBOARD">ACS_CKBOARD (in module curses)</a>
</li>
      <li><a href="library/curses.html#curses.ACS_DARROW">ACS_DARROW (in module curses)</a>
</li>
      <li><a href="library/curses.html#curses.ACS_DEGREE">ACS_DEGREE (in module curses)</a>
</li>
      <li><a href="library/curses.html#curses.ACS_DIAMOND">ACS_DIAMOND (in module curses)</a>
</li>
      <li><a href="library/curses.html#curses.ACS_GEQUAL">ACS_GEQUAL (in module curses)</a>
</li>
      <li><a href="library/curses.html#curses.ACS_HLINE">ACS_HLINE (in module curses)</a>
</li>
      <li><a href="library/curses.html#curses.ACS_LANTERN">ACS_LANTERN (in module curses)</a>
</li>
      <li><a href="library/curses.html#curses.ACS_LARROW">ACS_LARROW (in module curses)</a>
</li>
      <li><a href="library/curses.html#curses.ACS_LEQUAL">ACS_LEQUAL (in module curses)</a>
</li>
      <li><a href="library/curses.html#curses.ACS_LLCORNER">ACS_LLCORNER (in module curses)</a>
</li>
      <li><a href="library/curses.html#curses.ACS_LRCORNER">ACS_LRCORNER (in module curses)</a>
</li>
      <li><a href="library/curses.html#curses.ACS_LTEE">ACS_LTEE (in module curses)</a>
</li>
      <li><a href="library/curses.html#curses.ACS_NEQUAL">ACS_NEQUAL (in module curses)</a>
</li>
      <li><a href="library/curses.html#curses.ACS_PI">ACS_PI (in module curses)</a>
</li>
      <li><a href="library/curses.html#curses.ACS_PLMINUS">ACS_PLMINUS (in module curses)</a>
</li>
      <li><a href="library/curses.html#curses.ACS_PLUS">ACS_PLUS (in module curses)</a>
</li>
      <li><a href="library/curses.html#curses.ACS_RARROW">ACS_RARROW (in module curses)</a>
</li>
      <li><a href="library/curses.html#curses.ACS_RTEE">ACS_RTEE (in module curses)</a>
</li>
      <li><a href="library/curses.html#curses.ACS_S1">ACS_S1 (in module curses)</a>
</li>
      <li><a href="library/curses.html#curses.ACS_S3">ACS_S3 (in module curses)</a>
</li>
      <li><a href="library/curses.html#curses.ACS_S7">ACS_S7 (in module curses)</a>
</li>
      <li><a href="library/curses.html#curses.ACS_S9">ACS_S9 (in module curses)</a>
</li>
      <li><a href="library/curses.html#curses.ACS_SBBS">ACS_SBBS (in module curses)</a>
</li>
      <li><a href="library/curses.html#curses.ACS_SBSB">ACS_SBSB (in module curses)</a>
</li>
      <li><a href="library/curses.html#curses.ACS_SBSS">ACS_SBSS (in module curses)</a>
</li>
      <li><a href="library/curses.html#curses.ACS_SSBB">ACS_SSBB (in module curses)</a>
</li>
      <li><a href="library/curses.html#curses.ACS_SSBS">ACS_SSBS (in module curses)</a>
</li>
      <li><a href="library/curses.html#curses.ACS_SSSB">ACS_SSSB (in module curses)</a>
</li>
      <li><a href="library/curses.html#curses.ACS_SSSS">ACS_SSSS (in module curses)</a>
</li>
      <li><a href="library/curses.html#curses.ACS_STERLING">ACS_STERLING (in module curses)</a>
</li>
      <li><a href="library/curses.html#curses.ACS_TTEE">ACS_TTEE (in module curses)</a>
</li>
      <li><a href="library/curses.html#curses.ACS_UARROW">ACS_UARROW (in module curses)</a>
</li>
      <li><a href="library/curses.html#curses.ACS_ULCORNER">ACS_ULCORNER (in module curses)</a>
</li>
      <li><a href="library/curses.html#curses.ACS_URCORNER">ACS_URCORNER (in module curses)</a>
</li>
      <li><a href="library/curses.html#curses.ACS_VLINE">ACS_VLINE (in module curses)</a>
</li>
      <li><a href="library/argparse.html#argparse.Action">Action (class in argparse)</a>
</li>
      <li><a href="library/optparse.html#optparse.Option.action">action (optparse.Option attribute)</a>
</li>
      <li><a href="library/optparse.html#optparse.Option.ACTIONS">ACTIONS (optparse.Option attribute)</a>
</li>
      <li><a href="library/sys.html#sys.activate_stack_trampoline">activate_stack_trampoline() (in module sys)</a>
</li>
      <li><a href="library/multiprocessing.html#multiprocessing.active_children">active_children() (in module multiprocessing)</a>
</li>
      <li><a href="library/threading.html#threading.active_count">active_count() (in module threading)</a>
</li>
      <li><a href="library/tkinter.font.html#tkinter.font.Font.actual">actual() (tkinter.font.Font method)</a>
</li>
      <li><a href="library/ast.html#ast.Add">Add (class in ast)</a>
</li>
      <li><a href="library/decimal.html#decimal.Context.add">add() (decimal.Context method)</a>

      <ul>
        <li><a href="library/stdtypes.html#frozenset.add">(frozenset method)</a>
</li>
        <li><a href="library/graphlib.html#graphlib.TopologicalSorter.add">(graphlib.TopologicalSorter method)</a>
</li>
        <li><a href="library/audioop.html#audioop.add">(in module audioop)</a>
</li>
        <li><a href="library/operator.html#operator.add">(in module operator)</a>
</li>
        <li><a href="library/mailbox.html#mailbox.Mailbox.add">(mailbox.Mailbox method)</a>
</li>
        <li><a href="library/mailbox.html#mailbox.Maildir.add">(mailbox.Maildir method)</a>
</li>
        <li><a href="library/msilib.html#msilib.RadioButtonGroup.add">(msilib.RadioButtonGroup method)</a>
</li>
        <li><a href="library/profile.html#pstats.Stats.add">(pstats.Stats method)</a>
</li>
        <li><a href="library/tarfile.html#tarfile.TarFile.add">(tarfile.TarFile method)</a>
</li>
        <li><a href="library/tkinter.ttk.html#tkinter.ttk.Notebook.add">(tkinter.ttk.Notebook method)</a>
</li>
      </ul></li>
      <li><a href="library/email.charset.html#email.charset.add_alias">add_alias() (in module email.charset)</a>
</li>
      <li><a href="library/email.message.html#email.message.EmailMessage.add_alternative">add_alternative() (email.message.EmailMessage method)</a>
</li>
      <li><a href="library/argparse.html#argparse.ArgumentParser.add_argument">add_argument() (argparse.ArgumentParser method)</a>
</li>
      <li><a href="library/argparse.html#argparse.ArgumentParser.add_argument_group">add_argument_group() (argparse.ArgumentParser method)</a>
</li>
      <li><a href="library/email.message.html#email.message.EmailMessage.add_attachment">add_attachment() (email.message.EmailMessage method)</a>
</li>
      <li><a href="library/wsgiref.html#wsgiref.handlers.BaseHandler.add_cgi_vars">add_cgi_vars() (wsgiref.handlers.BaseHandler method)</a>
</li>
      <li><a href="library/email.charset.html#email.charset.add_charset">add_charset() (in module email.charset)</a>
</li>
      <li><a href="library/asyncio-policy.html#asyncio.AbstractChildWatcher.add_child_handler">add_child_handler() (asyncio.AbstractChildWatcher method)</a>
</li>
      <li><a href="library/email.charset.html#email.charset.add_codec">add_codec() (in module email.charset)</a>
</li>
      <li><a href="library/http.cookiejar.html#http.cookiejar.CookieJar.add_cookie_header">add_cookie_header() (http.cookiejar.CookieJar method)</a>
</li>
      <li><a href="library/msilib.html#msilib.add_data">add_data() (in module msilib)</a>
</li>
      <li><a href="library/os.html#os.add_dll_directory">add_dll_directory() (in module os)</a>
</li>
      <li><a href="library/asyncio-future.html#asyncio.Future.add_done_callback">add_done_callback() (asyncio.Future method)</a>

      <ul>
        <li><a href="library/asyncio-task.html#asyncio.Task.add_done_callback">(asyncio.Task method)</a>
</li>
        <li><a href="library/concurrent.futures.html#concurrent.futures.Future.add_done_callback">(concurrent.futures.Future method)</a>
</li>
      </ul></li>
      <li><a href="library/gettext.html#gettext.NullTranslations.add_fallback">add_fallback() (gettext.NullTranslations method)</a>
</li>
      <li><a href="library/msilib.html#msilib.Directory.add_file">add_file() (msilib.Directory method)</a>
</li>
      <li><a href="library/mailbox.html#mailbox.MaildirMessage.add_flag">add_flag() (mailbox.MaildirMessage method)</a>

      <ul>
        <li><a href="library/mailbox.html#mailbox.mboxMessage.add_flag">(mailbox.mboxMessage method)</a>
</li>
        <li><a href="library/mailbox.html#mailbox.MMDFMessage.add_flag">(mailbox.MMDFMessage method)</a>
</li>
      </ul></li>
      <li><a href="library/mailbox.html#mailbox.Maildir.add_folder">add_folder() (mailbox.Maildir method)</a>

      <ul>
        <li><a href="library/mailbox.html#mailbox.MH.add_folder">(mailbox.MH method)</a>
</li>
      </ul></li>
      <li><a href="library/email.contentmanager.html#email.contentmanager.ContentManager.add_get_handler">add_get_handler() (email.contentmanager.ContentManager method)</a>
</li>
      <li><a href="library/urllib.request.html#urllib.request.OpenerDirector.add_handler">add_handler() (urllib.request.OpenerDirector method)</a>
</li>
      <li><a href="library/email.message.html#email.message.EmailMessage.add_header">add_header() (email.message.EmailMessage method)</a>

      <ul>
        <li><a href="library/email.compat32-message.html#email.message.Message.add_header">(email.message.Message method)</a>
</li>
        <li><a href="library/urllib.request.html#urllib.request.Request.add_header">(urllib.request.Request method)</a>
</li>
        <li><a href="library/wsgiref.html#wsgiref.headers.Headers.add_header">(wsgiref.headers.Headers method)</a>
</li>
      </ul></li>
      <li><a href="library/readline.html#readline.add_history">add_history() (in module readline)</a>
</li>
      <li><a href="library/mailbox.html#mailbox.BabylMessage.add_label">add_label() (mailbox.BabylMessage method)</a>
</li>
      <li><a href="library/argparse.html#argparse.ArgumentParser.add_mutually_exclusive_group">add_mutually_exclusive_group() (argparse.ArgumentParser method)</a>
</li>
      <li><a href="library/exceptions.html#BaseException.add_note">add_note() (BaseException method)</a>
</li>
      <li><a href="library/optparse.html#optparse.OptionParser.add_option">add_option() (optparse.OptionParser method)</a>
</li>
      <li><a href="library/urllib.request.html#urllib.request.BaseHandler.add_parent">add_parent() (urllib.request.BaseHandler method)</a>
</li>
      <li><a href="library/urllib.request.html#urllib.request.HTTPPasswordMgr.add_password">add_password() (urllib.request.HTTPPasswordMgr method)</a>

      <ul>
        <li><a href="library/urllib.request.html#urllib.request.HTTPPasswordMgrWithPriorAuth.add_password">(urllib.request.HTTPPasswordMgrWithPriorAuth method)</a>
</li>
      </ul></li>
      <li><a href="library/asyncio-eventloop.html#asyncio.loop.add_reader">add_reader() (asyncio.loop method)</a>
</li>
      <li><a href="library/email.message.html#email.message.EmailMessage.add_related">add_related() (email.message.EmailMessage method)</a>
</li>
      <li><a href="library/configparser.html#configparser.ConfigParser.add_section">add_section() (configparser.ConfigParser method)</a>

      <ul>
        <li><a href="library/configparser.html#configparser.RawConfigParser.add_section">(configparser.RawConfigParser method)</a>
</li>
      </ul></li>
      <li><a href="library/mailbox.html#mailbox.MHMessage.add_sequence">add_sequence() (mailbox.MHMessage method)</a>
</li>
      <li><a href="library/email.contentmanager.html#email.contentmanager.ContentManager.add_set_handler">add_set_handler() (email.contentmanager.ContentManager method)</a>
</li>
      <li><a href="library/asyncio-eventloop.html#asyncio.loop.add_signal_handler">add_signal_handler() (asyncio.loop method)</a>
</li>
      <li><a href="library/msilib.html#msilib.add_stream">add_stream() (in module msilib)</a>
</li>
      <li><a href="library/argparse.html#argparse.ArgumentParser.add_subparsers">add_subparsers() (argparse.ArgumentParser method)</a>
</li>
      <li><a href="library/msilib.html#msilib.add_tables">add_tables() (in module msilib)</a>
</li>
      <li><a href="library/mimetypes.html#mimetypes.add_type">add_type() (in module mimetypes)</a>

      <ul>
        <li><a href="library/mimetypes.html#mimetypes.MimeTypes.add_type">(mimetypes.MimeTypes method)</a>
</li>
      </ul></li>
      <li><a href="library/urllib.request.html#urllib.request.Request.add_unredirected_header">add_unredirected_header() (urllib.request.Request method)</a>
</li>
      <li><a href="library/asyncio-eventloop.html#asyncio.loop.add_writer">add_writer() (asyncio.loop method)</a>
</li>
      <li><a href="library/unittest.html#unittest.IsolatedAsyncioTestCase.addAsyncCleanup">addAsyncCleanup() (unittest.IsolatedAsyncioTestCase method)</a>
</li>
      <li><a href="library/sys.html#sys.addaudithook">addaudithook() (in module sys)</a>
</li>
      <li><a href="library/curses.html#curses.window.addch">addch() (curses.window method)</a>
</li>
      <li><a href="library/unittest.html#unittest.TestCase.addClassCleanup">addClassCleanup() (unittest.TestCase class method)</a>
</li>
      <li><a href="library/unittest.html#unittest.TestCase.addCleanup">addCleanup() (unittest.TestCase method)</a>
</li>
      <li><a href="library/turtle.html#turtle.Shape.addcomponent">addcomponent() (turtle.Shape method)</a>
</li>
      <li><a href="library/unittest.html#unittest.TestResult.addDuration">addDuration() (unittest.TestResult method)</a>
</li>
      <li><a href="library/unittest.html#unittest.TestResult.addError">addError() (unittest.TestResult method)</a>
</li>
      <li><a href="library/unittest.html#unittest.TestResult.addExpectedFailure">addExpectedFailure() (unittest.TestResult method)</a>
</li>
      <li><a href="library/unittest.html#unittest.TestResult.addFailure">addFailure() (unittest.TestResult method)</a>
</li>
      <li><a href="library/tarfile.html#tarfile.TarFile.addfile">addfile() (tarfile.TarFile method)</a>
</li>
      <li><a href="library/logging.html#logging.Handler.addFilter">addFilter() (logging.Handler method)</a>

      <ul>
        <li><a href="library/logging.html#logging.Logger.addFilter">(logging.Logger method)</a>
</li>
      </ul></li>
      <li><a href="library/logging.html#logging.Logger.addHandler">addHandler() (logging.Logger method)</a>
</li>
      <li><a href="library/urllib.request.html#urllib.response.addinfourl">addinfourl (class in urllib.response)</a>
</li>
      <li><a href="reference/expressions.html#index-70">addition</a>
</li>
      <li><a href="library/logging.html#logging.addLevelName">addLevelName() (in module logging)</a>
</li>
      <li><a href="library/unittest.html#unittest.addModuleCleanup">addModuleCleanup() (in module unittest)</a>
</li>
      <li><a href="library/curses.html#curses.window.addnstr">addnstr() (curses.window method)</a>
</li>
      <li><a href="library/modulefinder.html#modulefinder.AddPackagePath">AddPackagePath() (in module modulefinder)</a>
</li>
      <li><a href="library/email.headerregistry.html#email.headerregistry.Address.addr_spec">addr_spec (email.headerregistry.Address attribute)</a>
</li>
      <li><a href="library/email.headerregistry.html#email.headerregistry.Address">Address (class in email.headerregistry)</a>
</li>
      <li><a href="library/email.headerregistry.html#email.headerregistry.SingleAddressHeader.address">address (email.headerregistry.SingleAddressHeader attribute)</a>

      <ul>
        <li><a href="library/multiprocessing.html#multiprocessing.connection.Listener.address">(multiprocessing.connection.Listener attribute)</a>
</li>
        <li><a href="library/multiprocessing.html#multiprocessing.managers.BaseManager.address">(multiprocessing.managers.BaseManager attribute)</a>
</li>
      </ul></li>
      <li><a href="library/ipaddress.html#ipaddress.IPv4Network.address_exclude">address_exclude() (ipaddress.IPv4Network method)</a>

      <ul>
        <li><a href="library/ipaddress.html#ipaddress.IPv6Network.address_exclude">(ipaddress.IPv6Network method)</a>
</li>
      </ul></li>
      <li><a href="library/socketserver.html#socketserver.BaseServer.address_family">address_family (socketserver.BaseServer attribute)</a>
</li>
      <li><a href="library/http.server.html#http.server.BaseHTTPRequestHandler.address_string">address_string() (http.server.BaseHTTPRequestHandler method)</a>
</li>
      <li><a href="library/email.headerregistry.html#email.headerregistry.AddressHeader.addresses">addresses (email.headerregistry.AddressHeader attribute)</a>

      <ul>
        <li><a href="library/email.headerregistry.html#email.headerregistry.Group.addresses">(email.headerregistry.Group attribute)</a>
</li>
      </ul></li>
      <li><a href="library/email.headerregistry.html#email.headerregistry.AddressHeader">AddressHeader (class in email.headerregistry)</a>
</li>
      <li><a href="library/ctypes.html#ctypes.addressof">addressof() (in module ctypes)</a>
</li>
      <li><a href="library/ipaddress.html#ipaddress.AddressValueError">AddressValueError</a>
</li>
      <li><a href="library/turtle.html#turtle.addshape">addshape() (in module turtle)</a>
</li>
      <li><a href="library/site.html#site.addsitedir">addsitedir() (in module site)</a>
</li>
      <li><a href="library/unittest.html#unittest.TestResult.addSkip">addSkip() (unittest.TestResult method)</a>
</li>
      <li><a href="library/curses.html#curses.window.addstr">addstr() (curses.window method)</a>
</li>
      <li><a href="library/unittest.html#unittest.TestResult.addSubTest">addSubTest() (unittest.TestResult method)</a>
</li>
      <li><a href="library/unittest.html#unittest.TestResult.addSuccess">addSuccess() (unittest.TestResult method)</a>
</li>
      <li><a href="library/unittest.html#unittest.TestSuite.addTest">addTest() (unittest.TestSuite method)</a>
</li>
      <li><a href="library/unittest.html#unittest.TestSuite.addTests">addTests() (unittest.TestSuite method)</a>
</li>
      <li><a href="library/unittest.html#unittest.TestCase.addTypeEqualityFunc">addTypeEqualityFunc() (unittest.TestCase method)</a>
</li>
      <li><a href="library/unittest.html#unittest.TestResult.addUnexpectedSuccess">addUnexpectedSuccess() (unittest.TestResult method)</a>
</li>
      <li><a href="library/test.html#test.support.adjust_int_max_str_digits">adjust_int_max_str_digits() (in module test.support)</a>
</li>
      <li><a href="library/decimal.html#decimal.Decimal.adjusted">adjusted() (decimal.Decimal method)</a>
</li>
      <li><a href="library/zlib.html#zlib.adler32">adler32() (in module zlib)</a>
</li>
      <li><a href="library/audioop.html#index-1">ADPCM, Intel/DVI</a>
</li>
      <li><a href="library/audioop.html#audioop.adpcm2lin">adpcm2lin() (in module audioop)</a>
</li>
      <li><a href="library/socket.html#socket.AF_ALG">AF_ALG (in module socket)</a>
</li>
      <li><a href="library/socket.html#socket.AF_CAN">AF_CAN (in module socket)</a>
</li>
      <li><a href="library/socket.html#socket.AF_DIVERT">AF_DIVERT (in module socket)</a>
</li>
      <li><a href="library/socket.html#socket.AF_HYPERV">AF_HYPERV (in module socket)</a>
</li>
      <li><a href="library/socket.html#socket.AF_INET">AF_INET (in module socket)</a>
</li>
      <li><a href="library/socket.html#socket.AF_INET6">AF_INET6 (in module socket)</a>
</li>
      <li><a href="library/socket.html#socket.AF_LINK">AF_LINK (in module socket)</a>
</li>
      <li><a href="library/socket.html#socket.AF_PACKET">AF_PACKET (in module socket)</a>
</li>
      <li><a href="library/socket.html#socket.AF_QIPCRTR">AF_QIPCRTR (in module socket)</a>
</li>
      <li><a href="library/socket.html#socket.AF_RDS">AF_RDS (in module socket)</a>
</li>
      <li><a href="library/socket.html#socket.AF_UNIX">AF_UNIX (in module socket)</a>
</li>
      <li><a href="library/socket.html#socket.AF_UNSPEC">AF_UNSPEC (in module socket)</a>
</li>
      <li><a href="library/socket.html#socket.AF_VSOCK">AF_VSOCK (in module socket)</a>
</li>
      <li>
    aifc

      <ul>
        <li><a href="library/aifc.html#module-aifc">module</a>
</li>
      </ul></li>
      <li><a href="library/aifc.html#aifc.aifc.aifc">aifc() (aifc.aifc method)</a>
</li>
      <li><a href="library/aifc.html#index-0">AIFF</a>, <a href="library/chunk.html#index-0">[1]</a>
</li>
      <li><a href="library/aifc.html#aifc.aifc.aiff">aiff() (aifc.aifc method)</a>
</li>
      <li><a href="library/aifc.html#index-0">AIFF-C</a>, <a href="library/chunk.html#index-0">[1]</a>
</li>
      <li>
    aiter()

      <ul>
        <li><a href="library/functions.html#aiter">built-in function</a>
</li>
      </ul></li>
      <li><a href="library/signal.html#signal.alarm">alarm() (in module signal)</a>
</li>
      <li><a href="library/audioop.html#audioop.alaw2lin">alaw2lin() (in module audioop)</a>
</li>
      <li><a href="library/ssl.html#ssl.ALERT_DESCRIPTION_HANDSHAKE_FAILURE">ALERT_DESCRIPTION_HANDSHAKE_FAILURE (in module ssl)</a>
</li>
      <li><a href="library/ssl.html#ssl.ALERT_DESCRIPTION_INTERNAL_ERROR">ALERT_DESCRIPTION_INTERNAL_ERROR (in module ssl)</a>
</li>
      <li><a href="library/ssl.html#ssl.AlertDescription">AlertDescription (class in ssl)</a>
</li>
      <li><a href="library/sys.html#sys.hash_info.algorithm">algorithm (sys.hash_info attribute)</a>
</li>
      <li><a href="library/hashlib.html#hashlib.algorithms_available">algorithms_available (in module hashlib)</a>
</li>
      <li><a href="library/hashlib.html#hashlib.algorithms_guaranteed">algorithms_guaranteed (in module hashlib)</a>
</li>
      <li>
    Alias

      <ul>
        <li><a href="library/stdtypes.html#index-56">Generic</a>
</li>
      </ul></li>
      <li><a href="library/ast.html#ast.alias">alias (class in ast)</a>

      <ul>
        <li><a href="library/pdb.html#pdbcommand-alias">(pdb command)</a>
</li>
      </ul></li>
      <li><a href="library/ctypes.html#ctypes.alignment">alignment() (in module ctypes)</a>
</li>
      <li><a href="library/weakref.html#weakref.finalize.alive">alive (weakref.finalize attribute)</a>
</li>
      <li>
    all()

      <ul>
        <li><a href="library/functions.html#all">built-in function</a>
</li>
      </ul></li>
      <li><a href="library/asyncio-task.html#asyncio.ALL_COMPLETED">ALL_COMPLETED (in module asyncio)</a>

      <ul>
        <li><a href="library/concurrent.futures.html#concurrent.futures.ALL_COMPLETED">(in module concurrent.futures)</a>
</li>
      </ul></li>
      <li><a href="library/ftplib.html#ftplib.all_errors">all_errors (in module ftplib)</a>
</li>
      <li><a href="library/xml.sax.handler.html#xml.sax.handler.all_features">all_features (in module xml.sax.handler)</a>
</li>
      <li><a href="library/tracemalloc.html#tracemalloc.Filter.all_frames">all_frames (tracemalloc.Filter attribute)</a>
</li>
      <li><a href="library/xml.sax.handler.html#xml.sax.handler.all_properties">all_properties (in module xml.sax.handler)</a>
</li>
      <li><a href="library/importlib.html#importlib.machinery.all_suffixes">all_suffixes() (in module importlib.machinery)</a>
</li>
      <li><a href="library/asyncio-task.html#asyncio.all_tasks">all_tasks() (in module asyncio)</a>
</li>
      <li><a href="library/_thread.html#thread.allocate_lock">allocate_lock() (in module _thread)</a>
</li>
      <li><a href="c-api/typeobj.html#c.allocfunc">allocfunc (C type)</a>
</li>
      <li><a href="library/socketserver.html#socketserver.BaseServer.allow_reuse_address">allow_reuse_address (socketserver.BaseServer attribute)</a>
</li>
      <li><a href="library/http.cookiejar.html#http.cookiejar.DefaultCookiePolicy.allowed_domains">allowed_domains() (http.cookiejar.DefaultCookiePolicy method)</a>
</li>
      <li><a href="library/curses.ascii.html#curses.ascii.alt">alt() (in module curses.ascii)</a>
</li>
      <li><a href="library/locale.html#locale.ALT_DIGITS">ALT_DIGITS (in module locale)</a>
</li>
      <li><a href="library/os.html#os.altsep">altsep (in module os)</a>
</li>
      <li><a href="library/time.html#time.altzone">altzone (in module time)</a>
</li>
      <li><a href="library/test.html#test.support.ALWAYS_EQ">ALWAYS_EQ (in module test.support)</a>
</li>
  </ul></td>
  <td style="width: 33%; vertical-align: top;"><ul>
      <li><a href="library/optparse.html#optparse.Option.ALWAYS_TYPED_ACTIONS">ALWAYS_TYPED_ACTIONS (optparse.Option attribute)</a>
</li>
      <li><a href="library/optparse.html#optparse.AmbiguousOptionError">AmbiguousOptionError</a>
</li>
      <li><a href="library/token.html#token.AMPER">AMPER (in module token)</a>
</li>
      <li><a href="library/token.html#token.AMPEREQUAL">AMPEREQUAL (in module token)</a>
</li>
      <li><a href="library/importlib.resources.html#importlib.resources.Anchor">Anchor (class in importlib.resources)</a>
</li>
      <li><a href="library/pathlib.html#pathlib.PurePath.anchor">anchor (pathlib.PurePath attribute)</a>
</li>
      <li>
    and

      <ul>
        <li><a href="reference/expressions.html#index-75">bitwise</a>
</li>
        <li><a href="library/stdtypes.html#index-4">operator</a>, <a href="library/stdtypes.html#index-6">[1]</a>, <a href="reference/expressions.html#index-85">[2]</a>
</li>
      </ul></li>
      <li><a href="library/ast.html#ast.And">And (class in ast)</a>
</li>
      <li><a href="library/operator.html#operator.and_">and_() (in module operator)</a>
</li>
      <li>
    anext()

      <ul>
        <li><a href="library/functions.html#anext">built-in function</a>
</li>
      </ul></li>
      <li><a href="library/ast.html#ast.AnnAssign">AnnAssign (class in ast)</a>
</li>
      <li>
    annotated

      <ul>
        <li><a href="reference/simple_stmts.html#index-15">assignment</a>
</li>
      </ul></li>
      <li><a href="library/typing.html#typing.Annotated">Annotated (in module typing)</a>
</li>
      <li><a href="glossary.html#term-annotation"><strong>annotation</strong></a>

      <ul>
        <li><a href="library/stdtypes.html#index-55">type annotation; type hint</a>
</li>
      </ul></li>
      <li><a href="library/inspect.html#inspect.Parameter.annotation">annotation (inspect.Parameter attribute)</a>
</li>
      <li>
    annotations

      <ul>
        <li><a href="reference/compound_stmts.html#index-34">function</a>, <a href="tutorial/controlflow.html#index-6">[1]</a>
</li>
      </ul></li>
      <li>
    anonymous

      <ul>
        <li><a href="reference/expressions.html#index-91">function</a>
</li>
      </ul></li>
      <li><a href="library/multiprocessing.html#multiprocessing.connection.answer_challenge">answer_challenge() (in module multiprocessing.connection)</a>
</li>
      <li><a href="library/test.html#test.support.anticipate_failure">anticipate_failure() (in module test.support)</a>
</li>
      <li><a href="library/typing.html#typing.Any">Any (in module typing)</a>
</li>
      <li><a href="library/unittest.mock.html#unittest.mock.ANY">ANY (in module unittest.mock)</a>
</li>
      <li>
    any()

      <ul>
        <li><a href="library/functions.html#any">built-in function</a>
</li>
      </ul></li>
      <li><a href="library/inspect.html#inspect.BufferFlags.ANY_CONTIGUOUS">ANY_CONTIGUOUS (inspect.BufferFlags attribute)</a>
</li>
      <li><a href="library/typing.html#typing.AnyStr">AnyStr (in module typing)</a>
</li>
      <li><a href="library/sys.html#sys.api_version">api_version (in module sys)</a>
</li>
      <li><a href="library/sqlite3.html#sqlite3.apilevel">apilevel (in module sqlite3)</a>
</li>
      <li><a href="library/poplib.html#poplib.POP3.apop">apop() (poplib.POP3 method)</a>
</li>
      <li><a href="whatsnew/2.6.html#index-5">APPDATA</a>
</li>
      <li><a href="library/array.html#array.array.append">append() (array.array method)</a>

      <ul>
        <li><a href="library/collections.html#collections.deque.append">(collections.deque method)</a>
</li>
        <li><a href="library/email.header.html#email.header.Header.append">(email.header.Header method)</a>
</li>
        <li><a href="library/imaplib.html#imaplib.IMAP4.append">(imaplib.IMAP4 method)</a>
</li>
        <li><a href="library/msilib.html#msilib.CAB.append">(msilib.CAB method)</a>
</li>
        <li><a href="library/pipes.html#pipes.Template.append">(pipes.Template method)</a>
</li>
        <li><a href="library/stdtypes.html#index-24">(sequence method)</a>
</li>
        <li><a href="library/xml.etree.elementtree.html#xml.etree.ElementTree.Element.append">(xml.etree.ElementTree.Element method)</a>
</li>
      </ul></li>
      <li><a href="library/readline.html#readline.append_history_file">append_history_file() (in module readline)</a>
</li>
      <li><a href="library/xml.dom.html#xml.dom.Node.appendChild">appendChild() (xml.dom.Node method)</a>
</li>
      <li><a href="library/collections.html#collections.deque.appendleft">appendleft() (collections.deque method)</a>
</li>
      <li><a href="library/wsgiref.html#wsgiref.util.application_uri">application_uri() (in module wsgiref.util)</a>
</li>
      <li><a href="library/2to3.html#to3fixer-apply">apply (2to3 fixer)</a>
</li>
      <li><a href="library/multiprocessing.html#multiprocessing.pool.Pool.apply">apply() (multiprocessing.pool.Pool method)</a>
</li>
      <li><a href="library/multiprocessing.html#multiprocessing.pool.Pool.apply_async">apply_async() (multiprocessing.pool.Pool method)</a>
</li>
      <li><a href="library/inspect.html#inspect.BoundArguments.apply_defaults">apply_defaults() (inspect.BoundArguments method)</a>
</li>
      <li><a href="library/calendar.html#calendar.APRIL">APRIL (in module calendar)</a>
</li>
      <li><a href="library/platform.html#platform.architecture">architecture() (in module platform)</a>
</li>
      <li><a href="library/zipimport.html#zipimport.zipimporter.archive">archive (zipimport.zipimporter attribute)</a>
</li>
      <li><a href="library/tarfile.html#tarfile.AREGTYPE">AREGTYPE (in module tarfile)</a>
</li>
      <li><a href="library/reprlib.html#reprlib.aRepr">aRepr (in module reprlib)</a>
</li>
      <li><a href="library/ast.html#ast.arg">arg (class in ast)</a>
</li>
      <li>
    argparse

      <ul>
        <li><a href="library/argparse.html#module-argparse">module</a>
</li>
      </ul></li>
      <li><a href="library/exceptions.html#BaseException.args">args (BaseException attribute)</a>

      <ul>
        <li><a href="library/functools.html#functools.partial.args">(functools.partial attribute)</a>
</li>
        <li><a href="library/inspect.html#inspect.BoundArguments.args">(inspect.BoundArguments attribute)</a>
</li>
        <li><a href="library/pdb.html#pdbcommand-args">(pdb command)</a>
</li>
        <li><a href="library/subprocess.html#subprocess.CompletedProcess.args">(subprocess.CompletedProcess attribute)</a>
</li>
        <li><a href="library/subprocess.html#subprocess.Popen.args">(subprocess.Popen attribute)</a>
</li>
        <li><a href="library/typing.html#typing.ParamSpec.args">(typing.ParamSpec attribute)</a>
</li>
      </ul></li>
      <li><a href="library/test.html#test.support.args_from_interpreter_flags">args_from_interpreter_flags() (in module test.support)</a>
</li>
      <li><a href="library/ctypes.html#ctypes._CFuncPtr.argtypes">argtypes (ctypes._CFuncPtr attribute)</a>
</li>
      <li><a href="glossary.html#term-argument"><strong>argument</strong></a>

      <ul>
        <li><a href="reference/expressions.html#index-48">call semantics</a>
</li>
        <li><a href="faq/programming.html#index-1">difference from parameter</a>
</li>
        <li><a href="reference/datamodel.html#index-33">function</a>
</li>
        <li><a href="reference/compound_stmts.html#index-31">function definition</a>
</li>
      </ul></li>
      <li><a href="library/argparse.html#argparse.ArgumentDefaultsHelpFormatter">ArgumentDefaultsHelpFormatter (class in argparse)</a>
</li>
      <li><a href="library/argparse.html#argparse.ArgumentError">ArgumentError</a>, <a href="library/ctypes.html#ctypes.ArgumentError">[1]</a>
</li>
      <li><a href="library/argparse.html#argparse.ArgumentParser">ArgumentParser (class in argparse)</a>
</li>
      <li><a href="library/ast.html#ast.arguments">arguments (class in ast)</a>

      <ul>
        <li><a href="library/inspect.html#inspect.BoundArguments.arguments">(inspect.BoundArguments attribute)</a>
</li>
      </ul></li>
      <li><a href="library/argparse.html#argparse.ArgumentTypeError">ArgumentTypeError</a>
</li>
      <li><a href="c-api/init.html#index-29">argv (in module sys)</a>, <a href="library/sys.html#sys.argv">[1]</a>
</li>
      <li><a href="library/stdtypes.html#index-13">arithmetic</a>

      <ul>
        <li><a href="reference/expressions.html#index-1">conversion</a>
</li>
        <li><a href="reference/expressions.html#index-65">operation, binary</a>
</li>
        <li><a href="reference/expressions.html#index-60">operation, unary</a>
</li>
      </ul></li>
      <li><a href="library/exceptions.html#ArithmeticError">ArithmeticError</a>
</li>
      <li>
    array

      <ul>
        <li><a href="library/array.html#module-array">module</a>, <a href="library/stdtypes.html#index-40">[1]</a>, <a href="reference/datamodel.html#index-23">[2]</a>
</li>
      </ul></li>
      <li><a href="library/array.html#array.array">array (class in array)</a>
</li>
      <li><a href="library/ctypes.html#ctypes.Array">Array (class in ctypes)</a>
</li>
      <li><a href="library/multiprocessing.html#multiprocessing.Array">Array() (in module multiprocessing)</a>

      <ul>
        <li><a href="library/multiprocessing.html#multiprocessing.sharedctypes.Array">(in module multiprocessing.sharedctypes)</a>
</li>
        <li><a href="library/multiprocessing.html#multiprocessing.managers.SyncManager.Array">(multiprocessing.managers.SyncManager method)</a>
</li>
      </ul></li>
      <li><a href="library/array.html#index-0">arrays</a>
</li>
      <li><a href="library/sqlite3.html#sqlite3.Cursor.arraysize">arraysize (sqlite3.Cursor attribute)</a>
</li>
      <li><a href="library/nntplib.html#nntplib.NNTP.article">article() (nntplib.NNTP method)</a>
</li>
      <li>
    as

      <ul>
        <li><a href="reference/compound_stmts.html#index-10">except clause</a>
</li>
        <li><a href="reference/simple_stmts.html#index-35">import statement</a>
</li>
        <li><a href="reference/compound_stmts.html#index-16">keyword</a>, <a href="reference/compound_stmts.html#index-18">[1]</a>, <a href="reference/compound_stmts.html#index-9">[2]</a>, <a href="reference/simple_stmts.html#index-34">[3]</a>
</li>
        <li><a href="reference/compound_stmts.html#index-18">match statement</a>
</li>
        <li><a href="reference/compound_stmts.html#index-16">with statement</a>
</li>
      </ul></li>
      <li><a href="reference/compound_stmts.html#index-23">AS pattern, OR pattern, capture pattern, wildcard pattern</a>
</li>
      <li><a href="library/email.message.html#email.message.EmailMessage.as_bytes">as_bytes() (email.message.EmailMessage method)</a>

      <ul>
        <li><a href="library/email.compat32-message.html#email.message.Message.as_bytes">(email.message.Message method)</a>
</li>
      </ul></li>
      <li><a href="library/asyncio-task.html#asyncio.as_completed">as_completed() (in module asyncio)</a>

      <ul>
        <li><a href="library/concurrent.futures.html#concurrent.futures.as_completed">(in module concurrent.futures)</a>
</li>
      </ul></li>
      <li><a href="library/importlib.resources.html#importlib.resources.as_file">as_file() (in module importlib.resources)</a>
</li>
      <li><a href="library/decimal.html#decimal.Decimal.as_integer_ratio">as_integer_ratio() (decimal.Decimal method)</a>

      <ul>
        <li><a href="library/stdtypes.html#float.as_integer_ratio">(float method)</a>
</li>
        <li><a href="library/fractions.html#fractions.Fraction.as_integer_ratio">(fractions.Fraction method)</a>
</li>
        <li><a href="library/stdtypes.html#int.as_integer_ratio">(int method)</a>
</li>
      </ul></li>
      <li><a href="library/pathlib.html#pathlib.PurePath.as_posix">as_posix() (pathlib.PurePath method)</a>
</li>
      <li><a href="library/email.message.html#email.message.EmailMessage.as_string">as_string() (email.message.EmailMessage method)</a>

      <ul>
        <li><a href="library/email.compat32-message.html#email.message.Message.as_string">(email.message.Message method)</a>
</li>
      </ul></li>
      <li><a href="library/decimal.html#decimal.Decimal.as_tuple">as_tuple() (decimal.Decimal method)</a>
</li>
      <li><a href="library/pathlib.html#pathlib.PurePath.as_uri">as_uri() (pathlib.PurePath method)</a>
</li>
      <li><a href="reference/introduction.html#index-1">ASCII</a>, <a href="reference/lexical_analysis.html#index-17">[1]</a>
</li>
      <li>
    ascii

      <ul>
        <li><a href="library/re.html#re.ASCII">(in module re)</a>
</li>
        <li><a href="c-api/object.html#index-1">built-in function</a>
</li>
      </ul></li>
      <li>
    ascii()

      <ul>
        <li><a href="library/functions.html#ascii">built-in function</a>
</li>
      </ul></li>
      <li><a href="library/curses.ascii.html#curses.ascii.ascii">ascii() (in module curses.ascii)</a>
</li>
      <li><a href="library/string.html#string.ascii_letters">ascii_letters (in module string)</a>
</li>
      <li><a href="library/string.html#string.ascii_lowercase">ascii_lowercase (in module string)</a>
</li>
      <li><a href="library/string.html#string.ascii_uppercase">ascii_uppercase (in module string)</a>
</li>
      <li><a href="library/time.html#time.asctime">asctime() (in module time)</a>
</li>
      <li><a href="library/dataclasses.html#dataclasses.asdict">asdict() (in module dataclasses)</a>
</li>
      <li><a href="reference/expressions.html#agen.asend">asend() (agen method)</a>
</li>
      <li><a href="library/cmath.html#cmath.asin">asin() (in module cmath)</a>

      <ul>
        <li><a href="library/math.html#math.asin">(in module math)</a>
</li>
      </ul></li>
      <li><a href="library/cmath.html#cmath.asinh">asinh() (in module cmath)</a>

      <ul>
        <li><a href="library/math.html#math.asinh">(in module math)</a>
</li>
      </ul></li>
      <li><a href="library/tkinter.colorchooser.html#tkinter.colorchooser.askcolor">askcolor() (in module tkinter.colorchooser)</a>
</li>
      <li><a href="library/dialog.html#tkinter.filedialog.askdirectory">askdirectory() (in module tkinter.filedialog)</a>
</li>
      <li><a href="library/dialog.html#tkinter.simpledialog.askfloat">askfloat() (in module tkinter.simpledialog)</a>
</li>
      <li><a href="library/dialog.html#tkinter.simpledialog.askinteger">askinteger() (in module tkinter.simpledialog)</a>
</li>
      <li><a href="library/tkinter.messagebox.html#tkinter.messagebox.askokcancel">askokcancel() (in module tkinter.messagebox)</a>
</li>
      <li><a href="library/dialog.html#tkinter.filedialog.askopenfile">askopenfile() (in module tkinter.filedialog)</a>
</li>
      <li><a href="library/dialog.html#tkinter.filedialog.askopenfilename">askopenfilename() (in module tkinter.filedialog)</a>
</li>
      <li><a href="library/dialog.html#tkinter.filedialog.askopenfilenames">askopenfilenames() (in module tkinter.filedialog)</a>
</li>
      <li><a href="library/dialog.html#tkinter.filedialog.askopenfiles">askopenfiles() (in module tkinter.filedialog)</a>
</li>
      <li><a href="library/tkinter.messagebox.html#tkinter.messagebox.askquestion">askquestion() (in module tkinter.messagebox)</a>
</li>
      <li><a href="library/tkinter.messagebox.html#tkinter.messagebox.askretrycancel">askretrycancel() (in module tkinter.messagebox)</a>
</li>
      <li><a href="library/dialog.html#tkinter.filedialog.asksaveasfile">asksaveasfile() (in module tkinter.filedialog)</a>
</li>
      <li><a href="library/dialog.html#tkinter.filedialog.asksaveasfilename">asksaveasfilename() (in module tkinter.filedialog)</a>
</li>
      <li><a href="library/dialog.html#tkinter.simpledialog.askstring">askstring() (in module tkinter.simpledialog)</a>
</li>
      <li><a href="library/tkinter.messagebox.html#tkinter.messagebox.askyesno">askyesno() (in module tkinter.messagebox)</a>
</li>
      <li><a href="library/tkinter.messagebox.html#tkinter.messagebox.askyesnocancel">askyesnocancel() (in module tkinter.messagebox)</a>
</li>
      <li>
    assert

      <ul>
        <li><a href="reference/simple_stmts.html#index-18"><strong>statement</strong></a>, <a href="library/exceptions.html#index-4">[1]</a>
</li>
      </ul></li>
      <li><a href="library/ast.html#ast.Assert">Assert (class in ast)</a>
</li>
      <li><a href="library/unittest.mock.html#unittest.mock.AsyncMock.assert_any_await">assert_any_await() (unittest.mock.AsyncMock method)</a>
</li>
      <li><a href="library/unittest.mock.html#unittest.mock.Mock.assert_any_call">assert_any_call() (unittest.mock.Mock method)</a>
</li>
      <li><a href="library/unittest.mock.html#unittest.mock.AsyncMock.assert_awaited">assert_awaited() (unittest.mock.AsyncMock method)</a>
</li>
      <li><a href="library/unittest.mock.html#unittest.mock.AsyncMock.assert_awaited_once">assert_awaited_once() (unittest.mock.AsyncMock method)</a>
</li>
      <li><a href="library/unittest.mock.html#unittest.mock.AsyncMock.assert_awaited_once_with">assert_awaited_once_with() (unittest.mock.AsyncMock method)</a>
</li>
      <li><a href="library/unittest.mock.html#unittest.mock.AsyncMock.assert_awaited_with">assert_awaited_with() (unittest.mock.AsyncMock method)</a>
</li>
      <li><a href="library/unittest.mock.html#unittest.mock.Mock.assert_called">assert_called() (unittest.mock.Mock method)</a>
</li>
      <li><a href="library/unittest.mock.html#unittest.mock.Mock.assert_called_once">assert_called_once() (unittest.mock.Mock method)</a>
</li>
      <li><a href="library/unittest.mock.html#unittest.mock.Mock.assert_called_once_with">assert_called_once_with() (unittest.mock.Mock method)</a>
</li>
      <li><a href="library/unittest.mock.html#unittest.mock.Mock.assert_called_with">assert_called_with() (unittest.mock.Mock method)</a>
</li>
      <li><a href="library/unittest.mock.html#unittest.mock.AsyncMock.assert_has_awaits">assert_has_awaits() (unittest.mock.AsyncMock method)</a>
</li>
      <li><a href="library/unittest.mock.html#unittest.mock.Mock.assert_has_calls">assert_has_calls() (unittest.mock.Mock method)</a>
</li>
      <li><a href="library/typing.html#typing.assert_never">assert_never() (in module typing)</a>
</li>
      <li><a href="library/unittest.mock.html#unittest.mock.AsyncMock.assert_not_awaited">assert_not_awaited() (unittest.mock.AsyncMock method)</a>
</li>
      <li><a href="library/unittest.mock.html#unittest.mock.Mock.assert_not_called">assert_not_called() (unittest.mock.Mock method)</a>
</li>
      <li><a href="library/test.html#test.support.script_helper.assert_python_failure">assert_python_failure() (in module test.support.script_helper)</a>
</li>
      <li><a href="library/test.html#test.support.script_helper.assert_python_ok">assert_python_ok() (in module test.support.script_helper)</a>
</li>
      <li><a href="library/typing.html#typing.assert_type">assert_type() (in module typing)</a>
</li>
      <li><a href="library/unittest.html#unittest.TestCase.assertAlmostEqual">assertAlmostEqual() (unittest.TestCase method)</a>
</li>
      <li><a href="library/unittest.html#unittest.TestCase.assertCountEqual">assertCountEqual() (unittest.TestCase method)</a>
</li>
      <li><a href="library/unittest.html#unittest.TestCase.assertDictEqual">assertDictEqual() (unittest.TestCase method)</a>
</li>
      <li><a href="library/unittest.html#unittest.TestCase.assertEqual">assertEqual() (unittest.TestCase method)</a>
</li>
      <li><a href="library/unittest.html#unittest.TestCase.assertFalse">assertFalse() (unittest.TestCase method)</a>
</li>
      <li><a href="library/unittest.html#unittest.TestCase.assertGreater">assertGreater() (unittest.TestCase method)</a>
</li>
      <li><a href="library/unittest.html#unittest.TestCase.assertGreaterEqual">assertGreaterEqual() (unittest.TestCase method)</a>
</li>
      <li><a href="library/unittest.html#unittest.TestCase.assertIn">assertIn() (unittest.TestCase method)</a>
</li>
      <li><a href="library/test.html#test.support.bytecode_helper.BytecodeTestCase.assertInBytecode">assertInBytecode() (test.support.bytecode_helper.BytecodeTestCase method)</a>
</li>
      <li><a href="library/exceptions.html#AssertionError">AssertionError</a>

      <ul>
        <li><a href="reference/simple_stmts.html#index-19">exception</a>
</li>
      </ul></li>
      <li>
    assertions

      <ul>
        <li><a href="reference/simple_stmts.html#index-18">debugging</a>
</li>
      </ul></li>
      <li><a href="library/unittest.html#unittest.TestCase.assertIs">assertIs() (unittest.TestCase method)</a>
</li>
      <li><a href="library/unittest.html#unittest.TestCase.assertIsInstance">assertIsInstance() (unittest.TestCase method)</a>
</li>
      <li><a href="library/unittest.html#unittest.TestCase.assertIsNone">assertIsNone() (unittest.TestCase method)</a>
</li>
      <li><a href="library/unittest.html#unittest.TestCase.assertIsNot">assertIsNot() (unittest.TestCase method)</a>
</li>
      <li><a href="library/unittest.html#unittest.TestCase.assertIsNotNone">assertIsNotNone() (unittest.TestCase method)</a>
</li>
      <li><a href="library/unittest.html#unittest.TestCase.assertLess">assertLess() (unittest.TestCase method)</a>
</li>
      <li><a href="library/unittest.html#unittest.TestCase.assertLessEqual">assertLessEqual() (unittest.TestCase method)</a>
</li>
      <li><a href="library/unittest.html#unittest.TestCase.assertListEqual">assertListEqual() (unittest.TestCase method)</a>
</li>
      <li><a href="library/unittest.html#unittest.TestCase.assertLogs">assertLogs() (unittest.TestCase method)</a>
</li>
      <li><a href="library/unittest.html#unittest.TestCase.assertMultiLineEqual">assertMultiLineEqual() (unittest.TestCase method)</a>
</li>
      <li><a href="library/unittest.html#unittest.TestCase.assertNoLogs">assertNoLogs() (unittest.TestCase method)</a>
</li>
      <li><a href="library/unittest.html#unittest.TestCase.assertNotAlmostEqual">assertNotAlmostEqual() (unittest.TestCase method)</a>
</li>
      <li><a href="library/unittest.html#unittest.TestCase.assertNotEqual">assertNotEqual() (unittest.TestCase method)</a>
</li>
      <li><a href="library/unittest.html#unittest.TestCase.assertNotIn">assertNotIn() (unittest.TestCase method)</a>
</li>
      <li><a href="library/test.html#test.support.bytecode_helper.BytecodeTestCase.assertNotInBytecode">assertNotInBytecode() (test.support.bytecode_helper.BytecodeTestCase method)</a>
</li>
      <li><a href="library/unittest.html#unittest.TestCase.assertNotIsInstance">assertNotIsInstance() (unittest.TestCase method)</a>
</li>
      <li><a href="library/unittest.html#unittest.TestCase.assertNotRegex">assertNotRegex() (unittest.TestCase method)</a>
</li>
      <li><a href="library/unittest.html#unittest.TestCase.assertRaises">assertRaises() (unittest.TestCase method)</a>
</li>
      <li><a href="library/unittest.html#unittest.TestCase.assertRaisesRegex">assertRaisesRegex() (unittest.TestCase method)</a>
</li>
      <li><a href="library/unittest.html#unittest.TestCase.assertRegex">assertRegex() (unittest.TestCase method)</a>
</li>
      <li><a href="library/2to3.html#to3fixer-asserts">asserts (2to3 fixer)</a>
</li>
      <li><a href="library/unittest.html#unittest.TestCase.assertSequenceEqual">assertSequenceEqual() (unittest.TestCase method)</a>
</li>
      <li><a href="library/unittest.html#unittest.TestCase.assertSetEqual">assertSetEqual() (unittest.TestCase method)</a>
</li>
      <li><a href="library/unittest.html#unittest.TestCase.assertTrue">assertTrue() (unittest.TestCase method)</a>
</li>
      <li><a href="library/unittest.html#unittest.TestCase.assertTupleEqual">assertTupleEqual() (unittest.TestCase method)</a>
</li>
      <li><a href="library/unittest.html#unittest.TestCase.assertWarns">assertWarns() (unittest.TestCase method)</a>
</li>
      <li><a href="library/unittest.html#unittest.TestCase.assertWarnsRegex">assertWarnsRegex() (unittest.TestCase method)</a>
</li>
      <li><a href="library/ast.html#ast.Assign">Assign (class in ast)</a>
</li>
      <li>
    assignment

      <ul>
        <li><a href="reference/simple_stmts.html#index-15">annotated</a>
</li>
        <li><a href="reference/simple_stmts.html#index-4">attribute</a>, <a href="reference/simple_stmts.html#index-8">[1]</a>
</li>
        <li><a href="reference/simple_stmts.html#index-14">augmented</a>
</li>
        <li><a href="reference/datamodel.html#index-50">class attribute</a>
</li>
        <li><a href="reference/datamodel.html#index-54">class instance attribute</a>
</li>
        <li><a href="library/stdtypes.html#index-24">slice</a>
</li>
        <li><a href="reference/simple_stmts.html#index-12">slicing</a>
</li>
        <li><a href="reference/datamodel.html#index-22">statement</a>, <a href="reference/simple_stmts.html#index-4">[1]</a>
</li>
        <li><a href="library/stdtypes.html#index-24">subscript</a>
</li>
        <li><a href="reference/simple_stmts.html#index-9">subscription</a>
</li>
        <li><a href="reference/simple_stmts.html#index-6">target list</a>
</li>
      </ul></li>
      <li><a href="reference/expressions.html#index-87">assignment expression</a>
</li>
      <li>
    ast

      <ul>
        <li><a href="library/ast.html#module-ast">module</a>
</li>
      </ul></li>
      <li><a href="library/ast.html#ast.AST">AST (class in ast)</a>
</li>
      <li>
    ast command line option

      <ul>
        <li><a href="library/ast.html#cmdoption-ast-h">--help</a>
</li>
        <li><a href="library/ast.html#cmdoption-ast-a">--include-attributes</a>
</li>
        <li><a href="library/ast.html#cmdoption-ast-indent">--indent</a>
</li>
        <li><a href="library/ast.html#cmdoption-ast-mode">--mode</a>
</li>
        <li><a href="library/ast.html#cmdoption-ast-no-type-comments">--no-type-comments</a>
</li>
        <li><a href="library/ast.html#cmdoption-ast-a">-a</a>
</li>
        <li><a href="library/ast.html#cmdoption-ast-h">-h</a>
</li>
        <li><a href="library/ast.html#cmdoption-ast-i">-i</a>
</li>
        <li><a href="library/ast.html#cmdoption-ast-m">-m</a>
</li>
      </ul></li>
      <li><a href="library/datetime.html#datetime.datetime.astimezone">astimezone() (datetime.datetime method)</a>
</li>
      <li><a href="library/dataclasses.html#dataclasses.astuple">astuple() (in module dataclasses)</a>
</li>
      <li>
    async

      <ul>
        <li><a href="reference/compound_stmts.html#index-50">keyword</a>
</li>
      </ul></li>
      <li><a href="library/token.html#token.ASYNC">ASYNC (in module token)</a>
</li>
      <li>
    async def

      <ul>
        <li><a href="reference/compound_stmts.html#index-49">statement</a>
</li>
      </ul></li>
      <li>
    async for

      <ul>
        <li><a href="reference/expressions.html#index-12">in comprehensions</a>
</li>
        <li><a href="reference/compound_stmts.html#index-51">statement</a>
</li>
      </ul></li>
      <li>
    async with

      <ul>
        <li><a href="reference/compound_stmts.html#index-52">statement</a>
</li>
      </ul></li>
      <li><a href="library/contextlib.html#contextlib.AsyncContextDecorator">AsyncContextDecorator (class in contextlib)</a>
</li>
      <li><a href="library/typing.html#typing.AsyncContextManager">AsyncContextManager (class in typing)</a>
</li>
      <li><a href="library/contextlib.html#contextlib.asynccontextmanager">asynccontextmanager() (in module contextlib)</a>
</li>
      <li><a href="library/contextlib.html#contextlib.AsyncExitStack">AsyncExitStack (class in contextlib)</a>
</li>
      <li><a href="library/ast.html#ast.AsyncFor">AsyncFor (class in ast)</a>
</li>
      <li><a href="library/ast.html#ast.AsyncFunctionDef">AsyncFunctionDef (class in ast)</a>
</li>
      <li><a href="library/collections.abc.html#collections.abc.AsyncGenerator">AsyncGenerator (class in collections.abc)</a>

      <ul>
        <li><a href="library/typing.html#typing.AsyncGenerator">(class in typing)</a>
</li>
      </ul></li>
      <li><a href="library/types.html#types.AsyncGeneratorType">AsyncGeneratorType (in module types)</a>
</li>
      <li>
    asynchat

      <ul>
        <li><a href="library/asynchat.html#module-asynchat">module</a>
</li>
      </ul></li>
      <li><a href="glossary.html#term-asynchronous-context-manager"><strong>asynchronous context manager</strong></a>
</li>
      <li><a href="glossary.html#term-asynchronous-generator"><strong>asynchronous generator</strong></a>

      <ul>
        <li><a href="reference/datamodel.html#index-41">asynchronous iterator</a>
</li>
        <li><a href="reference/datamodel.html#index-41">function</a>
</li>
      </ul></li>
      <li><a href="glossary.html#term-asynchronous-generator-iterator"><strong>asynchronous generator iterator</strong></a>
</li>
      <li><a href="glossary.html#term-asynchronous-iterable"><strong>asynchronous iterable</strong></a>
</li>
      <li><a href="glossary.html#term-asynchronous-iterator"><strong>asynchronous iterator</strong></a>
</li>
      <li>
    asynchronous-generator

      <ul>
        <li><a href="reference/expressions.html#index-35">object</a>
</li>
      </ul></li>
      <li>
    asyncio

      <ul>
        <li><a href="library/asyncio.html#module-asyncio">module</a>
</li>
      </ul></li>
      <li><a href="library/asyncio-subprocess.html#asyncio.subprocess.DEVNULL">asyncio.subprocess.DEVNULL (built-in variable)</a>
</li>
      <li><a href="library/asyncio-subprocess.html#asyncio.subprocess.PIPE">asyncio.subprocess.PIPE (built-in variable)</a>
</li>
      <li><a href="library/asyncio-subprocess.html#asyncio.subprocess.Process">asyncio.subprocess.Process (built-in class)</a>
</li>
      <li><a href="library/asyncio-subprocess.html#asyncio.subprocess.STDOUT">asyncio.subprocess.STDOUT (built-in variable)</a>
</li>
      <li><a href="library/collections.abc.html#collections.abc.AsyncIterable">AsyncIterable (class in collections.abc)</a>

      <ul>
        <li><a href="library/typing.html#typing.AsyncIterable">(class in typing)</a>
</li>
      </ul></li>
      <li><a href="library/collections.abc.html#collections.abc.AsyncIterator">AsyncIterator (class in collections.abc)</a>

      <ul>
        <li><a href="library/typing.html#typing.AsyncIterator">(class in typing)</a>
</li>
      </ul></li>
      <li><a href="library/unittest.mock.html#unittest.mock.AsyncMock">AsyncMock (class in unittest.mock)</a>
</li>
      <li>
    asyncore

      <ul>
        <li><a href="library/asyncore.html#module-asyncore">module</a>
</li>
      </ul></li>
      <li><a href="library/multiprocessing.html#multiprocessing.pool.AsyncResult">AsyncResult (class in multiprocessing.pool)</a>
</li>
      <li><a href="library/unittest.html#unittest.IsolatedAsyncioTestCase.asyncSetUp">asyncSetUp() (unittest.IsolatedAsyncioTestCase method)</a>
</li>
      <li><a href="library/unittest.html#unittest.IsolatedAsyncioTestCase.asyncTearDown">asyncTearDown() (unittest.IsolatedAsyncioTestCase method)</a>
</li>
      <li><a href="library/ast.html#ast.AsyncWith">AsyncWith (class in ast)</a>
</li>
      <li><a href="library/token.html#token.AT">AT (in module token)</a>
</li>
      <li><a href="library/asyncio-stream.html#asyncio.StreamReader.at_eof">at_eof() (asyncio.StreamReader method)</a>
</li>
      <li><a href="library/cmath.html#cmath.atan">atan() (in module cmath)</a>

      <ul>
        <li><a href="library/math.html#math.atan">(in module math)</a>
</li>
      </ul></li>
      <li><a href="library/math.html#math.atan2">atan2() (in module math)</a>
</li>
      <li><a href="library/cmath.html#cmath.atanh">atanh() (in module cmath)</a>

      <ul>
        <li><a href="library/math.html#math.atanh">(in module math)</a>
</li>
      </ul></li>
      <li><a href="library/token.html#token.ATEQUAL">ATEQUAL (in module token)</a>
</li>
      <li>
    atexit

      <ul>
        <li><a href="library/atexit.html#module-atexit">module</a>
</li>
      </ul></li>
      <li><a href="library/weakref.html#weakref.finalize.atexit">atexit (weakref.finalize attribute)</a>
</li>
      <li><a href="reference/expressions.html#agen.athrow">athrow() (agen method)</a>
</li>
      <li><a href="library/locale.html#locale.atof">atof() (in module locale)</a>
</li>
      <li><a href="library/locale.html#locale.atoi">atoi() (in module locale)</a>
</li>
      <li><a href="reference/expressions.html#index-2">atom</a>
</li>
      <li><a href="library/email.compat32-message.html#email.message.Message.attach">attach() (email.message.Message method)</a>
</li>
      <li><a href="library/asyncio-policy.html#asyncio.AbstractChildWatcher.attach_loop">attach_loop() (asyncio.AbstractChildWatcher method)</a>
</li>
      <li><a href="library/unittest.mock.html#unittest.mock.Mock.attach_mock">attach_mock() (unittest.mock.Mock method)</a>
</li>
      <li><a href="library/pyexpat.html#xml.parsers.expat.xmlparser.AttlistDeclHandler">AttlistDeclHandler() (xml.parsers.expat.xmlparser method)</a>
</li>
      <li><a href="library/operator.html#operator.attrgetter">attrgetter() (in module operator)</a>
</li>
      <li><a href="library/xml.etree.elementtree.html#xml.etree.ElementTree.Element.attrib">attrib (xml.etree.ElementTree.Element attribute)</a>
</li>
      <li><a href="glossary.html#term-attribute"><strong>attribute</strong></a>, <a href="reference/datamodel.html#index-5">[1]</a>

      <ul>
        <li><a href="reference/simple_stmts.html#index-4">assignment</a>, <a href="reference/simple_stmts.html#index-8">[1]</a>
</li>
        <li><a href="reference/datamodel.html#index-50">assignment, class</a>
</li>
        <li><a href="reference/datamodel.html#index-54">assignment, class instance</a>
</li>
        <li><a href="reference/datamodel.html#index-49">class</a>
</li>
        <li><a href="reference/datamodel.html#index-53">class instance</a>
</li>
        <li><a href="reference/simple_stmts.html#index-23">deletion</a>
</li>
        <li><a href="reference/datamodel.html#index-5">generic special</a>
</li>
        <li><a href="reference/expressions.html#index-39">reference</a>
</li>
        <li><a href="reference/datamodel.html#index-5">special</a>
</li>
      </ul></li>
      <li><a href="library/ast.html#ast.Attribute">Attribute (class in ast)</a>
</li>
      <li><a href="library/exceptions.html#AttributeError">AttributeError</a>

      <ul>
        <li><a href="reference/expressions.html#index-40">exception</a>
</li>
      </ul></li>
      <li><a href="library/xml.dom.html#xml.dom.Node.attributes">attributes (xml.dom.Node attribute)</a>
</li>
      <li><a href="library/xml.sax.reader.html#xml.sax.xmlreader.AttributesImpl">AttributesImpl (class in xml.sax.xmlreader)</a>
</li>
      <li><a href="library/xml.sax.reader.html#xml.sax.xmlreader.AttributesNSImpl">AttributesNSImpl (class in xml.sax.xmlreader)</a>
</li>
      <li><a href="library/curses.html#curses.window.attroff">attroff() (curses.window method)</a>
</li>
      <li><a href="library/curses.html#curses.window.attron">attron() (curses.window method)</a>
</li>
      <li><a href="library/curses.html#curses.window.attrset">attrset() (curses.window method)</a>
</li>
      <li><a href="library/aifc.html#index-0">Audio Interchange File Format</a>, <a href="library/chunk.html#index-0">[1]</a>
</li>
      <li><a href="library/sunau.html#sunau.AUDIO_FILE_ENCODING_ADPCM_G721">AUDIO_FILE_ENCODING_ADPCM_G721 (in module sunau)</a>
</li>
      <li><a href="library/sunau.html#sunau.AUDIO_FILE_ENCODING_ADPCM_G722">AUDIO_FILE_ENCODING_ADPCM_G722 (in module sunau)</a>
</li>
      <li><a href="library/sunau.html#sunau.AUDIO_FILE_ENCODING_ADPCM_G723_3">AUDIO_FILE_ENCODING_ADPCM_G723_3 (in module sunau)</a>
</li>
      <li><a href="library/sunau.html#sunau.AUDIO_FILE_ENCODING_ADPCM_G723_5">AUDIO_FILE_ENCODING_ADPCM_G723_5 (in module sunau)</a>
</li>
      <li><a href="library/sunau.html#sunau.AUDIO_FILE_ENCODING_ALAW_8">AUDIO_FILE_ENCODING_ALAW_8 (in module sunau)</a>
</li>
      <li><a href="library/sunau.html#sunau.AUDIO_FILE_ENCODING_DOUBLE">AUDIO_FILE_ENCODING_DOUBLE (in module sunau)</a>
</li>
      <li><a href="library/sunau.html#sunau.AUDIO_FILE_ENCODING_FLOAT">AUDIO_FILE_ENCODING_FLOAT (in module sunau)</a>
</li>
      <li><a href="library/sunau.html#sunau.AUDIO_FILE_ENCODING_LINEAR_16">AUDIO_FILE_ENCODING_LINEAR_16 (in module sunau)</a>
</li>
      <li><a href="library/sunau.html#sunau.AUDIO_FILE_ENCODING_LINEAR_24">AUDIO_FILE_ENCODING_LINEAR_24 (in module sunau)</a>
</li>
      <li><a href="library/sunau.html#sunau.AUDIO_FILE_ENCODING_LINEAR_32">AUDIO_FILE_ENCODING_LINEAR_32 (in module sunau)</a>
</li>
      <li><a href="library/sunau.html#sunau.AUDIO_FILE_ENCODING_LINEAR_8">AUDIO_FILE_ENCODING_LINEAR_8 (in module sunau)</a>
</li>
      <li><a href="library/sunau.html#sunau.AUDIO_FILE_ENCODING_MULAW_8">AUDIO_FILE_ENCODING_MULAW_8 (in module sunau)</a>
</li>
      <li><a href="library/sunau.html#sunau.AUDIO_FILE_MAGIC">AUDIO_FILE_MAGIC (in module sunau)</a>
</li>
      <li><a href="library/ossaudiodev.html#index-1">AUDIODEV</a>
</li>
      <li>
    audioop

      <ul>
        <li><a href="library/audioop.html#module-audioop">module</a>
</li>
      </ul></li>
      <li><a href="library/audit_events.html#index-0">audit events</a>
</li>
      <li><a href="library/sys.html#sys.audit">audit() (in module sys)</a>
</li>
      <li><a href="library/sys.html#index-2">auditing</a>
</li>
      <li><a href="library/ast.html#ast.AugAssign">AugAssign (class in ast)</a>
</li>
      <li>
    augmented

      <ul>
        <li><a href="reference/simple_stmts.html#index-14">assignment</a>
</li>
      </ul></li>
      <li><a href="library/calendar.html#calendar.AUGUST">AUGUST (in module calendar)</a>
</li>
      <li><a href="library/ftplib.html#ftplib.FTP_TLS.auth">auth() (ftplib.FTP_TLS method)</a>

      <ul>
        <li><a href="library/smtplib.html#smtplib.SMTP.auth">(smtplib.SMTP method)</a>
</li>
      </ul></li>
      <li><a href="library/imaplib.html#imaplib.IMAP4.authenticate">authenticate() (imaplib.IMAP4 method)</a>
</li>
      <li><a href="library/multiprocessing.html#multiprocessing.AuthenticationError">AuthenticationError</a>
</li>
      <li><a href="library/netrc.html#netrc.netrc.authenticators">authenticators() (netrc.netrc method)</a>
</li>
      <li><a href="library/multiprocessing.html#multiprocessing.Process.authkey">authkey (multiprocessing.Process attribute)</a>
</li>
      <li><a href="library/enum.html#enum.auto">auto (class in enum)</a>
</li>
      <li><a href="library/sqlite3.html#sqlite3.Connection.autocommit">autocommit (sqlite3.Connection attribute)</a>
</li>
      <li><a href="library/timeit.html#timeit.Timer.autorange">autorange() (timeit.Timer method)</a>
</li>
      <li><a href="library/zoneinfo.html#zoneinfo.available_timezones">available_timezones() (in module zoneinfo)</a>
</li>
      <li><a href="library/audioop.html#audioop.avg">avg() (in module audioop)</a>
</li>
      <li><a href="library/audioop.html#audioop.avgpp">avgpp() (in module audioop)</a>
</li>
      <li><a href="library/shutil.html#shutil.rmtree.avoids_symlink_attacks">avoids_symlink_attacks (shutil.rmtree attribute)</a>
</li>
      <li>
    await

      <ul>
        <li><a href="reference/expressions.html#index-13">in comprehensions</a>
</li>
        <li><a href="reference/compound_stmts.html#index-50">keyword</a>, <a href="reference/expressions.html#index-58">[1]</a>
</li>
      </ul></li>
      <li><a href="library/ast.html#ast.Await">Await (class in ast)</a>
</li>
      <li><a href="library/token.html#token.AWAIT">AWAIT (in module token)</a>
</li>
      <li><a href="library/unittest.mock.html#unittest.mock.AsyncMock.await_args">await_args (unittest.mock.AsyncMock attribute)</a>
</li>
      <li><a href="library/unittest.mock.html#unittest.mock.AsyncMock.await_args_list">await_args_list (unittest.mock.AsyncMock attribute)</a>
</li>
      <li><a href="library/unittest.mock.html#unittest.mock.AsyncMock.await_count">await_count (unittest.mock.AsyncMock attribute)</a>
</li>
      <li><a href="glossary.html#term-awaitable"><strong>awaitable</strong></a>
</li>
      <li><a href="library/collections.abc.html#collections.abc.Awaitable">Awaitable (class in collections.abc)</a>

      <ul>
        <li><a href="library/typing.html#typing.Awaitable">(class in typing)</a>
</li>
      </ul></li>
  </ul></td>
</tr></table>


            <div class="clearer"></div>
          </div>
        </div>
      </div>
      <div class="sphinxsidebar" role="navigation" aria-label="Main">
        <div class="sphinxsidebarwrapper">
        </div>
<div id="sidebarbutton" title="Collapse sidebar">
<span>«</span>
</div>

      </div>
      <div class="clearer"></div>
    </div>  
    <div class="related" role="navigation" aria-label="Related">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="genindex.html" title="General Index"
             >index</a></li>
        <li class="right" >
          <a href="py-modindex.html" title="Python Module Index"
             >modules</a> |</li>

          <li><img src="_static/py.svg" alt="Python logo" style="vertical-align: middle; margin-top: -1px"/></li>
          <li><a href="https://www.python.org/">Python</a> &#187;</li>
          <li class="switchers">
            <div class="language_switcher_placeholder"></div>
            <div class="version_switcher_placeholder"></div>
          </li>
          <li>
              
          </li>
    <li id="cpython-language-and-version">
      <a href="index.html">3.12.9 Documentation</a> &#187;
    </li>

        <li class="nav-item nav-item-this"><a href="">Index</a></li>
                <li class="right">
                    

    <div class="inline-search" role="search">
        <form class="inline-search" action="search.html" method="get">
          <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" id="search-box" />
          <input type="submit" value="Go" />
        </form>
    </div>
                     |
                </li>
            <li class="right">
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label> |</li>
            
      </ul>
    </div>  
    <div class="footer">
    &copy; 
      <a href="copyright.html">
    
    Copyright
    
      </a>
     2001-2025, Python Software Foundation.
    <br />
    This page is licensed under the Python Software Foundation License Version 2.
    <br />
    Examples, recipes, and other code in the documentation are additionally licensed under the Zero Clause BSD License.
    <br />
    
      See <a href="/license.html">History and License</a> for more information.<br />
    
    
    <br />

    The Python Software Foundation is a non-profit corporation.
<a href="https://www.python.org/psf/donations/">Please donate.</a>
<br />
    <br />
      Last updated on Feb 04, 2025 (15:07 UTC).
    
      <a href="/bugs.html">Found a bug</a>?
    
    <br />

    Created using <a href="https://www.sphinx-doc.org/">Sphinx</a> 8.1.3.
    </div>

  </body>
</html>